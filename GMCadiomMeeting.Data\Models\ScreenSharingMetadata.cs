using GMCadiomMeeting.Shared.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GMCadiomMeeting.Data.Models;

/// <summary>
/// Represents the status of a screen sharing session
/// </summary>
public enum ScreenSharingStatus
{
    Starting = 0,
    Active = 1,
    Paused = 2,
    Stopped = 3,
    Error = 4
}

/// <summary>
/// Represents metadata for screen sharing sessions in a meeting
/// </summary>
[Table("ScreenSharingMetadata")]
public class ScreenSharingMetadata
{
    /// <summary>
    /// Unique identifier for the screen sharing session
    /// </summary>
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    /// <summary>
    /// Unique session identifier for the screen sharing stream
    /// </summary>
    [Required]
    [StringLength(255)]
    [Column(TypeName = "varchar(255)")]
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// Type of screen sharing (full screen, window, tab, etc.)
    /// </summary>
    [Required]
    public ScreenSharingType Type { get; set; } = ScreenSharingType.FullScreen;

    /// <summary>
    /// Current status of the screen sharing session
    /// </summary>
    [Required]
    public ScreenSharingStatus Status { get; set; } = ScreenSharingStatus.Starting;

    /// <summary>
    /// Quality level of the screen sharing
    /// </summary>
    [Required]
    public ScreenSharingQuality Quality { get; set; } = ScreenSharingQuality.Medium;

    /// <summary>
    /// Title of the shared content (window title, tab title, etc.)
    /// </summary>
    [StringLength(200)]
    [Column(TypeName = "varchar(200)")]
    public string? SharedContentTitle { get; set; }

    /// <summary>
    /// Application name being shared (if sharing a specific application)
    /// </summary>
    [StringLength(100)]
    [Column(TypeName = "varchar(100)")]
    public string? ApplicationName { get; set; }

    /// <summary>
    /// Resolution of the shared content (e.g., "1920x1080")
    /// </summary>
    [StringLength(20)]
    [Column(TypeName = "varchar(20)")]
    public string? Resolution { get; set; }

    /// <summary>
    /// Frame rate of the screen sharing (frames per second)
    /// </summary>
    public int? FrameRate { get; set; }

    /// <summary>
    /// Whether audio is being shared along with the screen
    /// </summary>
    [Required]
    public bool IsAudioShared { get; set; } = false;

    /// <summary>
    /// Whether the presenter has control permissions (can control shared screen)
    /// </summary>
    [Required]
    public bool HasControlPermission { get; set; } = false;

    /// <summary>
    /// Whether participants can request control of the shared screen
    /// </summary>
    [Required]
    public bool AllowControlRequests { get; set; } = false;

    /// <summary>
    /// Whether the screen sharing is being recorded
    /// </summary>
    [Required]
    public bool IsRecorded { get; set; } = false;

    /// <summary>
    /// URL to the recording of this screen sharing session
    /// </summary>
    [StringLength(500)]
    [Column(TypeName = "varchar(500)")]
    public string? RecordingUrl { get; set; }

    /// <summary>
    /// Bandwidth usage in kbps
    /// </summary>
    public int? BandwidthKbps { get; set; }

    /// <summary>
    /// Technical metadata as JSON (codec info, stream details, etc.)
    /// </summary>
    [Column(TypeName = "json")]
    public string? TechnicalMetadata { get; set; }

    /// <summary>
    /// Performance metrics as JSON (latency, packet loss, etc.)
    /// </summary>
    [Column(TypeName = "json")]
    public string? PerformanceMetrics { get; set; }

    /// <summary>
    /// When the screen sharing session started
    /// </summary>
    [Required]
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// When the screen sharing session ended
    /// </summary>
    public DateTime? EndedAt { get; set; }

    /// <summary>
    /// Total duration of the screen sharing session in seconds
    /// </summary>
    public int? DurationSeconds { get; set; }

    /// <summary>
    /// Reason for ending the session (if applicable)
    /// </summary>
    [StringLength(200)]
    [Column(TypeName = "varchar(200)")]
    public string? EndReason { get; set; }

    /// <summary>
    /// When this record was created
    /// </summary>
    [Required]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// When this record was last updated
    /// </summary>
    [Required]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Foreign Keys

    /// <summary>
    /// ID of the meeting this screen sharing session belongs to
    /// </summary>
    [Required]
    public int MeetingId { get; set; }

    /// <summary>
    /// ID of the user who is sharing their screen
    /// </summary>
    [Required]
    public int SharingUserId { get; set; }

    // Navigation Properties

    /// <summary>
    /// The meeting this screen sharing session belongs to
    /// </summary>
    [ForeignKey(nameof(MeetingId))]
    public virtual Meeting Meeting { get; set; } = null!;

    /// <summary>
    /// The user who is sharing their screen
    /// </summary>
    [ForeignKey(nameof(SharingUserId))]
    public virtual User SharingUser { get; set; } = null!;
}
