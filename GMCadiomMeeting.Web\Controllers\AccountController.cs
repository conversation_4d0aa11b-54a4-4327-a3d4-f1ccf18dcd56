using GMCadiomMeeting.Shared.ViewModels.User.Login;
using GMCadiomMeeting.Shared.ViewModels.User.RegisterUser;
using GMCadiomMeeting.Web.Services;
using Microsoft.AspNetCore.Mvc;

namespace GMCadiomMeeting.Web.Controllers;

public class AccountController : Controller
{
    private readonly IAuthService _authService;
    private readonly ILogger<AccountController> _logger;

    public AccountController(IAuthService authService, ILogger<AccountController> logger)
    {
        _authService = authService;
        _logger = logger;
    }

    [HttpGet]
    public IActionResult Login(string? returnUrl = null)
    {
        if (_authService.IsAuthenticated())
        {
            return RedirectToAction("Dashboard", "Home");
        }

        ViewData["ReturnUrl"] = returnUrl;
        return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Login(LoginRequest request, string? returnUrl = null)
    {
        ViewData["ReturnUrl"] = returnUrl;

        if (!ModelState.IsValid)
        {
            return View(request);
        }

        var result = await _authService.LoginAsync(request);

        if (result)
        {
            _logger.LogInformation("User {Email} logged in successfully", request.Email);

            if (!string.IsNullOrEmpty(returnUrl) && Url.IsLocalUrl(returnUrl))
            {
                return Redirect(returnUrl);
            }

            return RedirectToAction("Dashboard", "Home");
        }

        ModelState.AddModelError(string.Empty, "Invalid email or password.");
        return View(request);
    }

    [HttpGet]
    public IActionResult Register()
    {
        if (_authService.IsAuthenticated())
        {
            return RedirectToAction("Dashboard", "Home");
        }

        return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Register(RegisterUserRequest request)
    {
        if (!ModelState.IsValid)
        {
            return View(request);
        }

        var result = await _authService.RegisterAsync(request);

        if (result)
        {
            _logger.LogInformation("User {Email} registered and logged in successfully", request.Email);
            return RedirectToAction("Dashboard", "Home");
        }

        ModelState.AddModelError(string.Empty, "Registration failed. Please try again.");
        return View(request);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Logout()
    {
        await _authService.LogoutAsync();
        _logger.LogInformation("User logged out");
        return RedirectToAction("Index", "Home");
    }

    [HttpGet]
    public async Task<IActionResult> Profile()
    {
        if (!_authService.IsAuthenticated())
        {
            return RedirectToAction("Login");
        }

        var user = await _authService.GetCurrentUserAsync();
        if (user == null)
        {
            return RedirectToAction("Login");
        }

        return View(user);
    }

    [HttpGet]
    public IActionResult AccessDenied()
    {
        return View();
    }
}
