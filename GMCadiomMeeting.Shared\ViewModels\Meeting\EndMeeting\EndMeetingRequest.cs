using System.ComponentModel.DataAnnotations;

namespace GMCadiomMeeting.Shared.ViewModels.Meeting.EndMeeting;

/// <summary>
/// Request model for ending a meeting
/// </summary>
public class EndMeetingRequest
{
    /// <summary>
    /// Meeting ID
    /// </summary>
    [Required(ErrorMessage = "Meeting ID is required")]
    public int MeetingId { get; set; }

    /// <summary>
    /// Reason for ending the meeting
    /// </summary>
    [StringLength(500, ErrorMessage = "Reason cannot exceed 500 characters")]
    public string? Reason { get; set; }
}
