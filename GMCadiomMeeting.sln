﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GMCadiomMeeting.Api", "GMCadiomMeeting.Api\GMCadiomMeeting.Api.csproj", "{6DDEA4A4-22E3-311C-AB0A-5EF7AA877173}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GMCadiomMeeting.Data", "GMCadiomMeeting.Data\GMCadiomMeeting.Data.csproj", "{506CBAC5-BEBE-8B78-82B9-1F00C967E497}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GMCadiomMeeting.Web", "GMCadiomMeeting.Web\GMCadiomMeeting.Web.csproj", "{68BE0D64-C7D3-ADFE-E265-D4AFAF8E4650}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GMCadiomMeeting.Shared", "GMCadiomMeeting.Shared\GMCadiomMeeting.Shared.csproj", "{C1201D9D-EDA4-4C8D-9B0A-19FCA60B5EBD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GMCadiomMeeting.Services", "GMCadiomMeeting.Services\GMCadiomMeeting.Services.csproj", "{7FEF2B45-0850-409D-A13F-0661547630EA}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{6DDEA4A4-22E3-311C-AB0A-5EF7AA877173}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6DDEA4A4-22E3-311C-AB0A-5EF7AA877173}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6DDEA4A4-22E3-311C-AB0A-5EF7AA877173}.Debug|x64.ActiveCfg = Debug|Any CPU
		{6DDEA4A4-22E3-311C-AB0A-5EF7AA877173}.Debug|x64.Build.0 = Debug|Any CPU
		{6DDEA4A4-22E3-311C-AB0A-5EF7AA877173}.Debug|x86.ActiveCfg = Debug|Any CPU
		{6DDEA4A4-22E3-311C-AB0A-5EF7AA877173}.Debug|x86.Build.0 = Debug|Any CPU
		{6DDEA4A4-22E3-311C-AB0A-5EF7AA877173}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6DDEA4A4-22E3-311C-AB0A-5EF7AA877173}.Release|Any CPU.Build.0 = Release|Any CPU
		{6DDEA4A4-22E3-311C-AB0A-5EF7AA877173}.Release|x64.ActiveCfg = Release|Any CPU
		{6DDEA4A4-22E3-311C-AB0A-5EF7AA877173}.Release|x64.Build.0 = Release|Any CPU
		{6DDEA4A4-22E3-311C-AB0A-5EF7AA877173}.Release|x86.ActiveCfg = Release|Any CPU
		{6DDEA4A4-22E3-311C-AB0A-5EF7AA877173}.Release|x86.Build.0 = Release|Any CPU
		{506CBAC5-BEBE-8B78-82B9-1F00C967E497}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{506CBAC5-BEBE-8B78-82B9-1F00C967E497}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{506CBAC5-BEBE-8B78-82B9-1F00C967E497}.Debug|x64.ActiveCfg = Debug|Any CPU
		{506CBAC5-BEBE-8B78-82B9-1F00C967E497}.Debug|x64.Build.0 = Debug|Any CPU
		{506CBAC5-BEBE-8B78-82B9-1F00C967E497}.Debug|x86.ActiveCfg = Debug|Any CPU
		{506CBAC5-BEBE-8B78-82B9-1F00C967E497}.Debug|x86.Build.0 = Debug|Any CPU
		{506CBAC5-BEBE-8B78-82B9-1F00C967E497}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{506CBAC5-BEBE-8B78-82B9-1F00C967E497}.Release|Any CPU.Build.0 = Release|Any CPU
		{506CBAC5-BEBE-8B78-82B9-1F00C967E497}.Release|x64.ActiveCfg = Release|Any CPU
		{506CBAC5-BEBE-8B78-82B9-1F00C967E497}.Release|x64.Build.0 = Release|Any CPU
		{506CBAC5-BEBE-8B78-82B9-1F00C967E497}.Release|x86.ActiveCfg = Release|Any CPU
		{506CBAC5-BEBE-8B78-82B9-1F00C967E497}.Release|x86.Build.0 = Release|Any CPU
		{68BE0D64-C7D3-ADFE-E265-D4AFAF8E4650}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{68BE0D64-C7D3-ADFE-E265-D4AFAF8E4650}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{68BE0D64-C7D3-ADFE-E265-D4AFAF8E4650}.Debug|x64.ActiveCfg = Debug|Any CPU
		{68BE0D64-C7D3-ADFE-E265-D4AFAF8E4650}.Debug|x64.Build.0 = Debug|Any CPU
		{68BE0D64-C7D3-ADFE-E265-D4AFAF8E4650}.Debug|x86.ActiveCfg = Debug|Any CPU
		{68BE0D64-C7D3-ADFE-E265-D4AFAF8E4650}.Debug|x86.Build.0 = Debug|Any CPU
		{68BE0D64-C7D3-ADFE-E265-D4AFAF8E4650}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{68BE0D64-C7D3-ADFE-E265-D4AFAF8E4650}.Release|Any CPU.Build.0 = Release|Any CPU
		{68BE0D64-C7D3-ADFE-E265-D4AFAF8E4650}.Release|x64.ActiveCfg = Release|Any CPU
		{68BE0D64-C7D3-ADFE-E265-D4AFAF8E4650}.Release|x64.Build.0 = Release|Any CPU
		{68BE0D64-C7D3-ADFE-E265-D4AFAF8E4650}.Release|x86.ActiveCfg = Release|Any CPU
		{68BE0D64-C7D3-ADFE-E265-D4AFAF8E4650}.Release|x86.Build.0 = Release|Any CPU
		{C1201D9D-EDA4-4C8D-9B0A-19FCA60B5EBD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C1201D9D-EDA4-4C8D-9B0A-19FCA60B5EBD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C1201D9D-EDA4-4C8D-9B0A-19FCA60B5EBD}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C1201D9D-EDA4-4C8D-9B0A-19FCA60B5EBD}.Debug|x64.Build.0 = Debug|Any CPU
		{C1201D9D-EDA4-4C8D-9B0A-19FCA60B5EBD}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C1201D9D-EDA4-4C8D-9B0A-19FCA60B5EBD}.Debug|x86.Build.0 = Debug|Any CPU
		{C1201D9D-EDA4-4C8D-9B0A-19FCA60B5EBD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C1201D9D-EDA4-4C8D-9B0A-19FCA60B5EBD}.Release|Any CPU.Build.0 = Release|Any CPU
		{C1201D9D-EDA4-4C8D-9B0A-19FCA60B5EBD}.Release|x64.ActiveCfg = Release|Any CPU
		{C1201D9D-EDA4-4C8D-9B0A-19FCA60B5EBD}.Release|x64.Build.0 = Release|Any CPU
		{C1201D9D-EDA4-4C8D-9B0A-19FCA60B5EBD}.Release|x86.ActiveCfg = Release|Any CPU
		{C1201D9D-EDA4-4C8D-9B0A-19FCA60B5EBD}.Release|x86.Build.0 = Release|Any CPU
		{7FEF2B45-0850-409D-A13F-0661547630EA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7FEF2B45-0850-409D-A13F-0661547630EA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7FEF2B45-0850-409D-A13F-0661547630EA}.Debug|x64.ActiveCfg = Debug|Any CPU
		{7FEF2B45-0850-409D-A13F-0661547630EA}.Debug|x64.Build.0 = Debug|Any CPU
		{7FEF2B45-0850-409D-A13F-0661547630EA}.Debug|x86.ActiveCfg = Debug|Any CPU
		{7FEF2B45-0850-409D-A13F-0661547630EA}.Debug|x86.Build.0 = Debug|Any CPU
		{7FEF2B45-0850-409D-A13F-0661547630EA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7FEF2B45-0850-409D-A13F-0661547630EA}.Release|Any CPU.Build.0 = Release|Any CPU
		{7FEF2B45-0850-409D-A13F-0661547630EA}.Release|x64.ActiveCfg = Release|Any CPU
		{7FEF2B45-0850-409D-A13F-0661547630EA}.Release|x64.Build.0 = Release|Any CPU
		{7FEF2B45-0850-409D-A13F-0661547630EA}.Release|x86.ActiveCfg = Release|Any CPU
		{7FEF2B45-0850-409D-A13F-0661547630EA}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {ED464B9D-BC7E-4320-B174-F9DCC3B0F736}
	EndGlobalSection
EndGlobal
