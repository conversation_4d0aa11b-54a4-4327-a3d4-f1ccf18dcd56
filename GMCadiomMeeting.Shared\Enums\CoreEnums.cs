using System.ComponentModel;

namespace GMCadiomMeeting.Shared.Enums;

/// <summary>
/// Represents the type of meeting
/// </summary>
public enum MeetingType
{
    /// <summary>
    /// One-on-one meeting between two participants
    /// </summary>
    [Description("One-on-One")]
    OneOnOne = 0,

    /// <summary>
    /// Group meeting with multiple participants
    /// </summary>
    [Description("Group Meeting")]
    Group = 1,

    /// <summary>
    /// Webinar with presenter and attendees
    /// </summary>
    [Description("Webinar")]
    Webinar = 2
}

/// <summary>
/// Represents the current status of a meeting
/// </summary>
public enum MeetingStatus
{
    /// <summary>
    /// Meeting is scheduled but not started
    /// </summary>
    [Description("Scheduled")]
    Scheduled = 0,

    /// <summary>
    /// Meeting is currently in progress
    /// </summary>
    [Description("In Progress")]
    InProgress = 1,

    /// <summary>
    /// Meeting has ended
    /// </summary>
    [Description("Ended")]
    Ended = 2,

    /// <summary>
    /// Meeting was cancelled
    /// </summary>
    [Description("Cancelled")]
    Cancelled = 3
}

/// <summary>
/// Represents the role of a participant in a meeting
/// </summary>
public enum ParticipantRole
{
    /// <summary>
    /// Meeting host with full control
    /// </summary>
    [Description("Host")]
    Host = 0,

    /// <summary>
    /// Co-host with administrative privileges
    /// </summary>
    [Description("Co-Host")]
    CoHost = 1,

    /// <summary>
    /// Presenter with screen sharing and presentation rights
    /// </summary>
    [Description("Presenter")]
    Presenter = 2,

    /// <summary>
    /// Regular attendee
    /// </summary>
    [Description("Attendee")]
    Attendee = 3,

    /// <summary>
    /// Observer with limited interaction rights
    /// </summary>
    [Description("Observer")]
    Observer = 4
}

/// <summary>
/// Represents the connection status of a participant
/// </summary>
public enum ConnectionStatus
{
    /// <summary>
    /// Participant is connected
    /// </summary>
    [Description("Connected")]
    Connected = 0,

    /// <summary>
    /// Participant is disconnected
    /// </summary>
    [Description("Disconnected")]
    Disconnected = 1,

    /// <summary>
    /// Participant is reconnecting
    /// </summary>
    [Description("Reconnecting")]
    Reconnecting = 2,

    /// <summary>
    /// Participant has left the meeting
    /// </summary>
    [Description("Left")]
    Left = 3
}

/// <summary>
/// Represents the status of an invitation
/// </summary>
public enum InvitationStatus
{
    /// <summary>
    /// Invitation is pending response
    /// </summary>
    [Description("Pending")]
    Pending = 0,

    /// <summary>
    /// Invitation was accepted
    /// </summary>
    [Description("Accepted")]
    Accepted = 1,

    /// <summary>
    /// Invitation was declined
    /// </summary>
    [Description("Declined")]
    Declined = 2,

    /// <summary>
    /// Invitation has expired
    /// </summary>
    [Description("Expired")]
    Expired = 3,

    /// <summary>
    /// Invitation was cancelled by sender
    /// </summary>
    [Description("Cancelled")]
    Cancelled = 4
}

/// <summary>
/// Represents the method used to send an invitation
/// </summary>
public enum InvitationMethod
{
    /// <summary>
    /// Invitation sent via email
    /// </summary>
    [Description("Email")]
    Email = 0,

    /// <summary>
    /// Invitation sent via SMS
    /// </summary>
    [Description("SMS")]
    SMS = 1,

    /// <summary>
    /// InApp invitation within the platform
    /// </summary>
    [Description("InApp")]
    InApp = 2,

    /// <summary>
    /// Invitation via external link
    /// </summary>
    [Description("Link")]
    Link = 3
}

/// <summary>
/// Represents the type of chat message
/// </summary>
public enum MessageType
{
    /// <summary>
    /// Undefined message
    /// </summary>
    [Description("Undefined")]
    Undefined = 0,

    /// <summary>
    /// System notification message
    /// </summary>
    [Description("System")]
    System,

    /// <summary>
    /// Regular text message
    /// </summary>
    [Description("Text")]
    Text,

    /// <summary>
    /// Emoji or reaction message
    /// </summary>
    [Description("Emoji")]
    Emoji,

    /// <summary>
    /// Regular image message
    /// </summary>
    [Description("Image")]
    Image,

    /// <summary>
    /// File attachment message
    /// </summary>
    [Description("File")]
    File,

    /// <summary>
    /// File attachment message
    /// </summary>
    [Description("File")]
    Link,
}

/// <summary>
/// Represents the scope of a chat message
/// </summary>
public enum MessageScope
{
    /// <summary>
    /// Message visible to all participants
    /// </summary>
    [Description("Public")]
    Public = 1,

    /// <summary>
    /// Direct message to specific participant
    /// </summary>
    [Description("Private")]
    Private,

    /// <summary>
    /// Message visible only to hosts and co-hosts
    /// </summary>
    [Description("Hosts")]
    Hosts,

    /// <summary>
    /// Message visible to hosts, co-hosts, and presenters
    /// </summary>
    [Description("Presenters")]
    Presenters,
}

/// <summary>
/// Represents the type of screen sharing
/// </summary>
public enum ScreenSharingType
{
    /// <summary>
    /// Sharing entire screen
    /// </summary>
    [Description("Full Screen")]
    FullScreen = 0,

    /// <summary>
    /// Sharing specific application window
    /// </summary>
    [Description("Application")]
    Application = 1,

    /// <summary>
    /// Sharing browser tab
    /// </summary>
    [Description("Browser Tab")]
    BrowserTab = 2,

    /// <summary>
    /// Sharing specific region of screen
    /// </summary>
    [Description("SpecificArea")]
    SpecificArea = 3
}

/// <summary>
/// Represents the quality level for video/audio
/// </summary>
public enum ScreenSharingQuality
{
    /// <summary>
    /// Low quality for bandwidth conservation // 720p, lower frame rate
    /// </summary>
    [Description("Low")]
    Low = 0,

    /// <summary>
    /// Medium quality for balanced performance // 1080p, standard frame rate
    /// </summary>
    [Description("Medium")]
    Medium = 1,

    /// <summary>
    /// High quality for best experience // 1080p, high frame rate
    /// </summary>
    [Description("High")]
    High = 2,

    /// <summary>
    /// Ultra high quality for premium experience // 4K, high frame rate
    /// </summary>
    [Description("Ultra")]
    Ultra = 3
}
