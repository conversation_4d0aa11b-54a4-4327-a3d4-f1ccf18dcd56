using GMCadiomMeeting.Data.Context;
using GMCadiomMeeting.Data.Models;
using GMCadiomMeeting.Shared.Enums;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace GMCadiomMeeting.Api.Controllers;

/// <summary>
/// Controller for managing meeting invitations
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class InvitationsController : ControllerBase
{
    private readonly GMCadiomMeetingDbContext _context;
    private readonly ILogger<InvitationsController> _logger;

    public InvitationsController(GMCadiomMeetingDbContext context, ILogger<InvitationsController> logger)
    {
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// Send invitations to a meeting
    /// </summary>
    [HttpPost("send")]
    public async Task<ActionResult<IEnumerable<InvitationResponse>>> SendInvitations(SendInvitationsRequest request)
    {
        try
        {
            // Verify meeting exists and sender has permission
            var meeting = await _context.Meetings
                .Include(m => m.HostUser)
                .FirstOrDefaultAsync(m => m.Id == request.MeetingId);

            if (meeting == null)
            {
                return NotFound($"Meeting with ID {request.MeetingId} not found");
            }

            // Check if sender is host or co-host
            var senderParticipant = await _context.MeetingParticipants
                .FirstOrDefaultAsync(p => p.MeetingId == request.MeetingId &&
                                         p.UserId == request.SentByUserId &&
                                         (p.Role == ParticipantRole.Host || p.Role == ParticipantRole.CoHost));

            if (senderParticipant == null && meeting.HostUserId != request.SentByUserId)
            {
                return Forbid("Only hosts and co-hosts can send invitations");
            }

            var invitations = new List<Invitation>();

            foreach (var invitee in request.Invitees)
            {
                // Check if invitation already exists
                var existingInvitation = await _context.Invitations
                    .FirstOrDefaultAsync(i => i.MeetingId == request.MeetingId &&
                                             ((i.InvitedUserId.HasValue && i.InvitedUserId == invitee.UserId) ||
                                              (!string.IsNullOrEmpty(i.InviteeEmail) && i.InviteeEmail == invitee.Email)));

                if (existingInvitation != null && existingInvitation.Status == InvitationStatus.Pending)
                {
                    continue; // Skip if pending invitation already exists
                }

                var invitation = new Invitation
                {
                    MeetingId = request.MeetingId,
                    SentByUserId = request.SentByUserId,
                    InvitedUserId = invitee.UserId,
                    InviteeEmail = invitee.Email,
                    InviteeName = invitee.Name,
                    InvitationToken = Guid.NewGuid().ToString(),
                    Status = InvitationStatus.Pending,
                    Method = InvitationMethod.Email,
                    InvitedRole = invitee.Role,
                    PersonalMessage = request.PersonalMessage,
                    ExpiresAt = request.ExpiresAt,
                    AllowJoinBeforeHost = request.AllowJoinBeforeHost
                };

                invitations.Add(invitation);
            }

            if (invitations.Any())
            {
                _context.Invitations.AddRange(invitations);
                await _context.SaveChangesAsync();

                // TODO: Send email notifications here
                _logger.LogInformation("Sent {Count} invitations for meeting {MeetingId}",
                    invitations.Count, request.MeetingId);
            }

            var responses = invitations.Select(i => new InvitationResponse
            {
                Id = i.Id,
                MeetingId = i.MeetingId,
                InvitationToken = i.InvitationToken,
                InviteeEmail = i.InviteeEmail,
                InviteeName = i.InviteeName,
                Status = i.Status,
                Method = i.Method,
                InvitedRole = i.InvitedRole,
                PersonalMessage = i.PersonalMessage,
                SentAt = i.SentAt,
                ExpiresAt = i.ExpiresAt
            }).ToList();

            return Ok(responses);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending invitations for meeting {MeetingId}", request.MeetingId);
            return StatusCode(500, "An error occurred while sending invitations");
        }
    }

    /// <summary>
    /// Get invitations for a meeting
    /// </summary>
    [HttpGet("meeting/{meetingId}")]
    public async Task<ActionResult<IEnumerable<InvitationResponse>>> GetMeetingInvitations(int meetingId)
    {
        try
        {
            var invitations = await _context.Invitations
                .Where(i => i.MeetingId == meetingId)
                .Include(i => i.SentByUser)
                .Include(i => i.InvitedUser)
                .Include(i => i.Meeting)
                .OrderByDescending(i => i.SentAt)
                .ToListAsync();

            var responses = invitations.Select(i => new InvitationResponse
            {
                Id = i.Id,
                MeetingId = i.MeetingId,
                InvitationToken = i.InvitationToken,
                InviteeEmail = i.InviteeEmail,
                InviteeName = i.InviteeName,
                Status = i.Status,
                Method = i.Method,
                InvitedRole = i.InvitedRole,
                PersonalMessage = i.PersonalMessage,
                SentAt = i.SentAt,
                ExpiresAt = i.ExpiresAt,
                RespondedAt = i.RespondedAt,
                SentByUser = i.SentByUser?.DisplayName,
                InvitedUser = i.InvitedUser?.DisplayName,
                MeetingTitle = i.Meeting?.Title
            }).ToList();

            return Ok(responses);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving invitations for meeting {MeetingId}", meetingId);
            return StatusCode(500, "An error occurred while retrieving invitations");
        }
    }

    /// <summary>
    /// Get invitations for a user
    /// </summary>
    [HttpGet("user/{userId}")]
    public async Task<ActionResult<IEnumerable<InvitationResponse>>> GetUserInvitations(int userId)
    {
        try
        {
            var invitations = await _context.Invitations
                .Where(i => i.InvitedUserId == userId)
                .Include(i => i.SentByUser)
                .Include(i => i.Meeting)
                    .ThenInclude(m => m.HostUser)
                .OrderByDescending(i => i.SentAt)
                .ToListAsync();

            var responses = invitations.Select(i => new InvitationResponse
            {
                Id = i.Id,
                MeetingId = i.MeetingId,
                InvitationToken = i.InvitationToken,
                Status = i.Status,
                Method = i.Method,
                InvitedRole = i.InvitedRole,
                PersonalMessage = i.PersonalMessage,
                SentAt = i.SentAt,
                ExpiresAt = i.ExpiresAt,
                RespondedAt = i.RespondedAt,
                SentByUser = i.SentByUser?.DisplayName,
                MeetingTitle = i.Meeting?.Title,
                MeetingScheduledStart = i.Meeting?.ScheduledStartTime,
                MeetingHost = i.Meeting?.HostUser?.DisplayName
            }).ToList();

            return Ok(responses);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving invitations for user {UserId}", userId);
            return StatusCode(500, "An error occurred while retrieving invitations");
        }
    }

    /// <summary>
    /// Respond to an invitation (accept/decline)
    /// </summary>
    [HttpPost("{invitationId}/respond")]
    public async Task<ActionResult<InvitationResponse>> RespondToInvitation(int invitationId, RespondToInvitationRequest request)
    {
        try
        {
            var invitation = await _context.Invitations
                .Include(i => i.Meeting)
                .FirstOrDefaultAsync(i => i.Id == invitationId);

            if (invitation == null)
            {
                return NotFound($"Invitation with ID {invitationId} not found");
            }

            if (invitation.Status != InvitationStatus.Pending)
            {
                return BadRequest("Invitation has already been responded to");
            }

            if (invitation.ExpiresAt.HasValue && invitation.ExpiresAt < DateTime.UtcNow)
            {
                invitation.Status = InvitationStatus.Expired;
                await _context.SaveChangesAsync();
                return BadRequest("Invitation has expired");
            }

            invitation.Status = request.Accept ? InvitationStatus.Accepted : InvitationStatus.Declined;
            invitation.RespondedAt = DateTime.UtcNow;
            invitation.UpdatedAt = DateTime.UtcNow;

            // If accepted, add user as participant
            if (request.Accept && invitation.InvitedUserId.HasValue)
            {
                var existingParticipant = await _context.MeetingParticipants
                    .FirstOrDefaultAsync(p => p.MeetingId == invitation.MeetingId &&
                                             p.UserId == invitation.InvitedUserId);

                if (existingParticipant == null)
                {
                    var participant = new MeetingParticipant
                    {
                        UserId = invitation.InvitedUserId.Value,
                        MeetingId = invitation.MeetingId,
                        Role = invitation.InvitedRole,
                        Status = ConnectionStatus.Disconnected,
                        CanShareScreen = invitation.InvitedRole != ParticipantRole.Observer,
                        CanUseChat = true,
                        CanUnmute = invitation.InvitedRole != ParticipantRole.Observer,
                        CanTurnOnCamera = invitation.InvitedRole != ParticipantRole.Observer
                    };

                    _context.MeetingParticipants.Add(participant);
                }
            }

            await _context.SaveChangesAsync();

            var response = new InvitationResponse
            {
                Id = invitation.Id,
                MeetingId = invitation.MeetingId,
                Status = invitation.Status,
                RespondedAt = invitation.RespondedAt,
                MeetingTitle = invitation.Meeting?.Title
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error responding to invitation {InvitationId}", invitationId);
            return StatusCode(500, "An error occurred while responding to the invitation");
        }
    }

    /// <summary>
    /// Join meeting via invitation token
    /// </summary>
    [HttpPost("join/{token}")]
    public async Task<ActionResult<JoinViaInvitationResponse>> JoinViaInvitation(string token, JoinViaInvitationRequest request)
    {
        try
        {
            var invitation = await _context.Invitations
                .Include(i => i.Meeting)
                .FirstOrDefaultAsync(i => i.InvitationToken == token);

            if (invitation == null)
            {
                return NotFound("Invalid invitation token");
            }

            if (invitation.ExpiresAt.HasValue && invitation.ExpiresAt < DateTime.UtcNow)
            {
                return BadRequest("Invitation has expired");
            }

            if (invitation.Meeting?.Status == MeetingStatus.Ended || invitation.Meeting?.Status == MeetingStatus.Cancelled)
            {
                return BadRequest("Meeting has ended or been cancelled");
            }

            // For external users, create a temporary participant record
            var participant = new MeetingParticipant
            {
                UserId = request.UserId ?? 0, // 0 for external users
                MeetingId = invitation.MeetingId,
                Role = invitation.InvitedRole,
                Status = ConnectionStatus.Connected,
                JoinedAt = DateTime.UtcNow,
                DisplayNameInMeeting = request.DisplayName ?? invitation.InviteeName,
                IsCameraEnabled = request.IsCameraEnabled,
                IsMicrophoneEnabled = request.IsMicrophoneEnabled
            };

            _context.MeetingParticipants.Add(participant);

            // Update invitation status if not already accepted
            if (invitation.Status == InvitationStatus.Pending)
            {
                invitation.Status = InvitationStatus.Accepted;
                invitation.RespondedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();

            var response = new JoinViaInvitationResponse
            {
                MeetingId = invitation.MeetingId,
                MeetingCode = invitation.Meeting?.MeetingCode ?? "",
                MeetingTitle = invitation.Meeting?.Title ?? "",
                ParticipantId = participant.Id,
                Role = participant.Role,
                DisplayName = participant.DisplayNameInMeeting
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error joining meeting via invitation token {Token}", token);
            return StatusCode(500, "An error occurred while joining the meeting");
        }
    }
}

// Request/Response DTOs
public class SendInvitationsRequest
{
    public int MeetingId { get; set; }
    public int SentByUserId { get; set; }
    public List<InviteeInfo> Invitees { get; set; } = new();
    public string? PersonalMessage { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public bool AllowJoinBeforeHost { get; set; } = false;
}

public class InviteeInfo
{
    public int? UserId { get; set; }
    public string? Email { get; set; }
    public string? Name { get; set; }
    public ParticipantRole Role { get; set; } = ParticipantRole.Attendee;
}

public class RespondToInvitationRequest
{
    public bool Accept { get; set; }
}

public class JoinViaInvitationRequest
{
    public int? UserId { get; set; }
    public string? DisplayName { get; set; }
    public bool IsCameraEnabled { get; set; } = false;
    public bool IsMicrophoneEnabled { get; set; } = false;
}

public class InvitationResponse
{
    public int Id { get; set; }
    public int MeetingId { get; set; }
    public string InvitationToken { get; set; } = string.Empty;
    public string? InviteeEmail { get; set; }
    public string? InviteeName { get; set; }
    public InvitationStatus Status { get; set; }
    public InvitationMethod Method { get; set; }
    public ParticipantRole InvitedRole { get; set; }
    public string? PersonalMessage { get; set; }
    public DateTime SentAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public DateTime? RespondedAt { get; set; }
    public string? SentByUser { get; set; }
    public string? InvitedUser { get; set; }
    public string? MeetingTitle { get; set; }
    public DateTime? MeetingScheduledStart { get; set; }
    public string? MeetingHost { get; set; }
}

public class JoinViaInvitationResponse
{
    public int MeetingId { get; set; }
    public string MeetingCode { get; set; } = string.Empty;
    public string MeetingTitle { get; set; } = string.Empty;
    public int ParticipantId { get; set; }
    public ParticipantRole Role { get; set; }
    public string? DisplayName { get; set; }
}
