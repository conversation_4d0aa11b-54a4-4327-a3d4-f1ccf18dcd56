using System.ComponentModel.DataAnnotations;

namespace GMCadiomMeeting.Shared.ViewModels.Participant.RemoveParticipant;

/// <summary>
/// Request model for removing a participant from a meeting
/// </summary>
public class RemoveParticipantRequest
{
    /// <summary>
    /// Meeting ID
    /// </summary>
    [Required(ErrorMessage = "Meeting ID is required")]
    public int MeetingId { get; set; }

    /// <summary>
    /// Participant ID to remove
    /// </summary>
    [Required(ErrorMessage = "Participant ID is required")]
    public int ParticipantId { get; set; }

    /// <summary>
    /// Reason for removal
    /// </summary>
    [StringLength(500, ErrorMessage = "Reason cannot exceed 500 characters")]
    public string? Reason { get; set; }
}
