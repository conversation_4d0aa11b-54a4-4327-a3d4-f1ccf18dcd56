﻿using GMCadiomMeeting.Data.Context;
using GMCadiomMeeting.Data.Models;
using GMCadiomMeeting.Shared.Enums;
using GMCadiomMeeting.Shared.ViewModels.Meeting;
using GMCadiomMeeting.Shared.ViewModels.Meeting.CreateMeeting;
using GMCadiomMeeting.Shared.ViewModels.Meeting.JoinMeeting;
using GMCadiomMeeting.Shared.ViewModels.Meeting.UpdateMeeting;
using GMCadiomMeeting.Shared.ViewModels.Meeting.UpdateParticipantPermissions;
using GMCadiomMeeting.Shared.ViewModels.Participant;
using GMCadiomMeeting.Shared.ViewModels.User;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace GMCadiomMeeting.Services.MeetingServices
{
    public class MeetingService(GMCadiomMeetingDbContext context, ILogger<MeetingService> logger)
    {
        /// <summary>
        /// Get all meetings for a specific user
        /// </summary>
        public async Task<IEnumerable<MeetingDto>> GetUserMeetingsAsync(int userId, MeetingStatus? status = null)
        {
            try
            {
                var query = context.Meetings
                    .Where(m => m.HostUserId == userId ||
                               m.Participants.Any(p => p.UserId == userId))
                    .Include(m => m.HostUser)
                    .Include(m => m.Participants)
                        .ThenInclude(p => p.User)
                    .AsQueryable();

                if (status.HasValue)
                {
                    query = query.Where(m => m.Status == status.Value);
                }

                return await query
                    .OrderBy(m => m.ScheduledStartTime)
                    .Select(m => new MeetingDto()
                    {
                        Id = m.Id,
                        Title = m.Title,
                        Description = m.Description,
                        MeetingCode = m.MeetingCode,
                        InvitationLink = m.InvitationLink,
                        Type = m.Type,
                        ScheduledStartTime = m.ScheduledStartTime,
                        ScheduledEndTime = m.ScheduledEndTime,
                        ActualStartTime = m.ActualStartTime,
                        ActualEndTime = m.ActualEndTime,
                        HostUserId = m.HostUserId,
                        MaxParticipants = m.MaxParticipants,
                        Password = m.Password,
                        IsRecordingEnabled = m.IsRecordingEnabled,
                        IsRecurring = m.IsRecurring,
                        RecurrencePattern = m.RecurrencePattern,
                        Status = m.Status,
                        CreatedAt = m.CreatedAt,
                        UpdatedAt = m.UpdatedAt,
                        HostUser = new UserDto()
                        {
                            Id = m.HostUser.Id,
                            DisplayName = m.HostUser.DisplayName,
                            FirstName = m.HostUser.FirstName,
                            LastName = m.HostUser.LastName,
                            IsEmailVerified = m.HostUser.IsEmailVerified,
                            LastLoginAt = m.HostUser.LastLoginAt,
                            TimeZone = m.HostUser.TimeZone,
                            Email = m.HostUser.Email,
                            IsActive = m.HostUser.IsActive,
                            CreatedAt = m.HostUser.CreatedAt,
                            ProfilePictureUrl = m.HostUser.ProfilePictureUrl,
                        },
                        Participants = m.Participants.Select(p => new MeetingParticipantDto
                        {
                            UserId = p.UserId,
                            DisplayNameInMeeting = p.DisplayNameInMeeting,
                            MeetingId = p.MeetingId,
                            Id = p.Id,
                            User = new UserDto
                            {
                                Id = p.User.Id,
                                DisplayName = p.User.DisplayName,
                                FirstName = p.User.FirstName,
                                LastName = p.User.LastName,
                                IsEmailVerified = p.User.IsEmailVerified,
                                LastLoginAt = p.User.LastLoginAt,
                                TimeZone = p.User.TimeZone,
                                Email = p.User.Email,
                                IsActive = p.User.IsActive,
                                CreatedAt = p.User.CreatedAt,
                                ProfilePictureUrl = p.User.ProfilePictureUrl
                            },
                            IsCameraEnabled = p.IsCameraEnabled,
                            IsMicrophoneEnabled = p.IsMicrophoneEnabled,
                            CreatedAt = p.CreatedAt,
                            DurationMinutes = p.DurationSeconds.HasValue ? p.DurationSeconds.Value / 60 : null,
                            IpAddress = p.IpAddress,
                            IsScreenSharing = p.IsScreenSharing,
                            UserAgent = p.UserAgent,
                            Role = p.Role,
                            Status = p.Status,
                            JoinedAt = p.JoinedAt,
                            LeftAt = p.LeftAt,
                        }).ToList(),
                        RecordingUrl = m.RecordingUrl,
                    })
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving meetings for user {UserId}", userId);
                throw new Exception("An error occurred while retrieving meetings");
            }
        }

        /// <summary>
        /// Get a specific meeting by ID
        /// </summary>
        public async Task<MeetingDto> GetMeeting(int id)
        {
            try
            {
                var meeting = await context.Meetings
                    .Include(m => m.HostUser)
                    .Include(m => m.Participants)
                        .ThenInclude(p => p.User)
                    .Include(m => m.Invitations)
                    .Include(m => m.ChatMessages)
                    .Include(m => m.ScreenSharingSessions)
                    .Select(m => new MeetingDto()
                    {
                        Id = m.Id,
                        Title = m.Title,
                        Description = m.Description,
                        MeetingCode = m.MeetingCode,
                        InvitationLink = m.InvitationLink,
                        Type = m.Type,
                        ScheduledStartTime = m.ScheduledStartTime,
                        ScheduledEndTime = m.ScheduledEndTime,
                        ActualStartTime = m.ActualStartTime,
                        ActualEndTime = m.ActualEndTime,
                        HostUserId = m.HostUserId,
                        MaxParticipants = m.MaxParticipants,
                        Password = m.Password,
                        IsRecordingEnabled = m.IsRecordingEnabled,
                        IsRecurring = m.IsRecurring,
                        RecurrencePattern = m.RecurrencePattern,
                        Status = m.Status,
                        CreatedAt = m.CreatedAt,
                        UpdatedAt = m.UpdatedAt,
                        HostUser = new UserDto()
                        {
                            Id = m.HostUser.Id,
                            DisplayName = m.HostUser.DisplayName,
                            FirstName = m.HostUser.FirstName,
                            LastName = m.HostUser.LastName,
                            IsEmailVerified = m.HostUser.IsEmailVerified,
                            LastLoginAt = m.HostUser.LastLoginAt,
                            TimeZone = m.HostUser.TimeZone,
                            Email = m.HostUser.Email,
                            IsActive = m.HostUser.IsActive,
                            CreatedAt = m.HostUser.CreatedAt,
                            ProfilePictureUrl = m.HostUser.ProfilePictureUrl,
                        },
                        Participants = m.Participants.Select(p => new MeetingParticipantDto
                        {
                            UserId = p.UserId,
                            DisplayNameInMeeting = p.DisplayNameInMeeting,
                            MeetingId = p.MeetingId,
                            Id = p.Id,
                            User = new UserDto
                            {
                                Id = p.User.Id,
                                DisplayName = p.User.DisplayName,
                                FirstName = p.User.FirstName,
                                LastName = p.User.LastName,
                                IsEmailVerified = p.User.IsEmailVerified,
                                LastLoginAt = p.User.LastLoginAt,
                                TimeZone = p.User.TimeZone,
                                Email = p.User.Email,
                                IsActive = p.User.IsActive,
                                CreatedAt = p.User.CreatedAt,
                                ProfilePictureUrl = p.User.ProfilePictureUrl
                            },
                            IsCameraEnabled = p.IsCameraEnabled,
                            IsMicrophoneEnabled = p.IsMicrophoneEnabled,
                            CreatedAt = p.CreatedAt,
                            DurationMinutes = p.DurationSeconds.HasValue ? p.DurationSeconds.Value / 60 : null,
                            IpAddress = p.IpAddress,
                            IsScreenSharing = p.IsScreenSharing,
                            UserAgent = p.UserAgent,
                            Role = p.Role,
                            Status = p.Status,
                            JoinedAt = p.JoinedAt,
                            LeftAt = p.LeftAt,
                        }).ToList(),
                        RecordingUrl = m.RecordingUrl,
                    })
                    .FirstOrDefaultAsync(m => m.Id == id);

                if (meeting == null)
                {
                    throw new KeyNotFoundException($"Meeting with ID {id} not found");
                }

                return meeting;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving meeting {MeetingId}", id);
                throw new Exception("An error occurred while retrieving the meeting");
            }
        }

        /// <summary>
        /// Create a new meeting
        /// </summary>
        public async Task<Meeting> CreateMeetingAsync(CreateMeetingRequest request)
        {
            try
            {
                // Validate host user exists
                var hostUser = await context.Users.FindAsync(request.HostUserId);
                if (hostUser == null || !hostUser.IsActive)
                {
                    throw new ArgumentException($"Host user with ID {request.HostUserId} not found or inactive");
                }

                // Validate scheduled times
                if (request.ScheduledStartTime >= request.ScheduledEndTime)
                {
                    throw new ArgumentException("Scheduled start time must be before end time");
                }

                if (request.ScheduledStartTime < DateTime.UtcNow.AddMinutes(-5))
                {
                    throw new ArgumentException("Cannot schedule meetings in the past");
                }

                // Generate unique meeting code and invitation link
                var meetingCode = await GenerateUniqueMeetingCodeAsync();
                var invitationLink = $"https://meet.gmcadiom.com/join/{Guid.NewGuid()}";

                var meeting = new Meeting
                {
                    Title = request.Title,
                    Description = request.Description,
                    MeetingCode = meetingCode,
                    InvitationLink = invitationLink,
                    Type = request.Type,
                    ScheduledStartTime = request.ScheduledStartTime,
                    ScheduledEndTime = request.ScheduledEndTime,
                    HostUserId = request.HostUserId,
                    MaxParticipants = request.MaxParticipants,
                    Password = request.Password,
                    IsRecordingEnabled = request.IsRecordingEnabled,
                    IsRecurring = request.IsRecurring,
                    RecurrencePattern = request.RecurrencePattern,
                    Status = MeetingStatus.Scheduled
                };

                context.Meetings.Add(meeting);
                await context.SaveChangesAsync();

                // Add host as a participant
                var hostParticipant = new MeetingParticipant
                {
                    UserId = request.HostUserId,
                    MeetingId = meeting.Id,
                    Role = ParticipantRole.Host,
                    Status = ConnectionStatus.Disconnected,
                    CanShareScreen = true,
                    CanUseChat = true,
                    CanUnmute = true,
                    CanTurnOnCamera = true
                };

                context.MeetingParticipants.Add(hostParticipant);
                await context.SaveChangesAsync();

                logger.LogInformation("Meeting {MeetingId} created by user {UserId}", meeting.Id, request.HostUserId);

                return meeting;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error creating meeting");
                throw new Exception("An error occurred while creating the meeting");
            }
        }

        /// <summary>
        /// Join a meeting by meeting code
        /// </summary>
        public async Task<MeetingParticipant> JoinMeetingAsync(string meetingCode, JoinMeetingRequest request)
        {
            try
            {
                var meeting = await context.Meetings.FirstOrDefaultAsync(m => m.MeetingCode == meetingCode);

                if (meeting == null)
                {
                    throw new ArgumentException($"Meeting with code {meetingCode} not found");
                }

                if (meeting.Status == MeetingStatus.Ended || meeting.Status == MeetingStatus.Cancelled)
                {
                    throw new InvalidOperationException("Cannot join a meeting that has ended or been cancelled");
                }

                // Check password if required
                if (!string.IsNullOrEmpty(meeting.Password) && meeting.Password != request.Password)
                {
                    throw new UnauthorizedAccessException("Invalid meeting password");
                }

                // Check if user exists
                var user = await context.Users.FindAsync(request.UserId);
                if (user == null || !user.IsActive)
                {
                    throw new ArgumentException($"User with ID {request.UserId} not found or inactive");
                }

                // Check if already a participant
                var existingParticipant = await context.MeetingParticipants
                    .FirstOrDefaultAsync(p => p.MeetingId == meeting.Id && p.UserId == request.UserId);

                if (existingParticipant != null)
                {
                    // Update existing participant
                    existingParticipant.Status = ConnectionStatus.Connected;
                    existingParticipant.JoinedAt = DateTime.UtcNow;
                    existingParticipant.IsCameraEnabled = request.IsCameraEnabled;
                    existingParticipant.IsMicrophoneEnabled = request.IsMicrophoneEnabled;
                    existingParticipant.IpAddress = request.IpAddress;
                    existingParticipant.UserAgent = request.UserAgent;
                    existingParticipant.UpdatedAt = DateTime.UtcNow;

                    await context.SaveChangesAsync();
                    return existingParticipant;
                }

                // Check participant limit
                var currentParticipantCount = await context.MeetingParticipants
                    .CountAsync(p => p.MeetingId == meeting.Id && p.Status == ConnectionStatus.Connected);

                if (currentParticipantCount >= meeting.MaxParticipants)
                {
                    throw new InvalidOperationException("Meeting has reached maximum participant limit");
                }

                // Create new participant
                var participant = new MeetingParticipant
                {
                    UserId = request.UserId,
                    MeetingId = meeting.Id,
                    Role = ParticipantRole.Attendee,
                    Status = ConnectionStatus.Connected,
                    JoinedAt = DateTime.UtcNow,
                    IsCameraEnabled = request.IsCameraEnabled,
                    IsMicrophoneEnabled = request.IsMicrophoneEnabled,
                    DisplayNameInMeeting = request.DisplayName,
                    IpAddress = request.IpAddress,
                    UserAgent = request.UserAgent
                };

                context.MeetingParticipants.Add(participant);
                await context.SaveChangesAsync();

                logger.LogInformation("User {UserId} joined meeting {MeetingId}", request.UserId, meeting.Id);

                return participant;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error joining meeting {MeetingCode}", meetingCode);
                throw new Exception("An error occurred while joining the meeting");
            }
        }

        public async Task<Meeting> UpdateMeetingAsync(int meetingId, UpdateMeetingRequest dto, int requestingUserId)
        {
            var meeting = await context.Meetings.FindAsync(meetingId);
            if (meeting == null)
            {
                throw new ArgumentException($"Meeting with ID {meetingId} not found");
            }

            // Only host can update meeting
            if (meeting.HostUserId != requestingUserId)
            {
                throw new UnauthorizedAccessException("Only the meeting host can update meeting details");
            }

            // Cannot update meetings that have ended
            if (meeting.Status == MeetingStatus.Ended)
            {
                throw new InvalidOperationException("Cannot update meetings that have ended");
            }

            // Update fields
            if (!string.IsNullOrEmpty(dto.Title))
                meeting.Title = dto.Title;

            if (dto.Description != null)
                meeting.Description = dto.Description;

            if (dto.ScheduledStartTime.HasValue)
            {
                if (dto.ScheduledStartTime.Value < DateTime.UtcNow.AddMinutes(-5))
                {
                    throw new ArgumentException("Cannot schedule meetings in the past");
                }
                meeting.ScheduledStartTime = dto.ScheduledStartTime.Value;
            }

            if (dto.ScheduledEndTime.HasValue)
            {
                if (dto.ScheduledEndTime.Value <= meeting.ScheduledStartTime)
                {
                    throw new ArgumentException("End time must be after start time");
                }
                meeting.ScheduledEndTime = dto.ScheduledEndTime.Value;
            }

            if (dto.MaxParticipants.HasValue)
                meeting.MaxParticipants = dto.MaxParticipants.Value;

            if (dto.Password != null)
                meeting.Password = dto.Password;

            meeting.IsRecordingEnabled = dto.IsRecordingEnabled;

            meeting.UpdatedAt = DateTime.UtcNow;

            await context.SaveChangesAsync();

            logger.LogInformation("Meeting {MeetingId} updated by user {UserId}", meetingId, requestingUserId);

            return meeting;
        }

        public async Task<Meeting> StartMeetingAsync(int meetingId, int hostUserId)
        {
            var meeting = await context.Meetings.FindAsync(meetingId);
            if (meeting == null)
            {
                throw new ArgumentException($"Meeting with ID {meetingId} not found");
            }

            if (meeting.HostUserId != hostUserId)
            {
                throw new UnauthorizedAccessException("Only the meeting host can start the meeting");
            }

            if (meeting.Status != MeetingStatus.Scheduled)
            {
                throw new InvalidOperationException($"Cannot start meeting with status {meeting.Status}");
            }

            meeting.Status = MeetingStatus.InProgress;
            meeting.ActualStartTime = DateTime.UtcNow;
            meeting.UpdatedAt = DateTime.UtcNow;

            await context.SaveChangesAsync();

            logger.LogInformation("Meeting {MeetingId} started by host {UserId}", meetingId, hostUserId);

            return meeting;
        }

        public async Task<Meeting> EndMeetingAsync(int meetingId, int hostUserId)
        {
            var meeting = await context.Meetings.FindAsync(meetingId);
            if (meeting == null)
            {
                throw new ArgumentException($"Meeting with ID {meetingId} not found");
            }

            if (meeting.HostUserId != hostUserId)
            {
                throw new UnauthorizedAccessException("Only the meeting host can end the meeting");
            }

            if (meeting.Status != MeetingStatus.InProgress)
            {
                throw new InvalidOperationException($"Cannot end meeting with status {meeting.Status}");
            }

            meeting.Status = MeetingStatus.Ended;
            meeting.ActualEndTime = DateTime.UtcNow;
            meeting.UpdatedAt = DateTime.UtcNow;

            // Update all participants to left status
            var participants = await context.MeetingParticipants
                .Where(p => p.MeetingId == meetingId && p.Status == ConnectionStatus.Connected)
                .ToListAsync();

            foreach (var participant in participants)
            {
                participant.Status = ConnectionStatus.Left;
                participant.LeftAt = DateTime.UtcNow;
                participant.UpdatedAt = DateTime.UtcNow;

                if (participant.JoinedAt.HasValue)
                {
                    participant.DurationSeconds = (int)(DateTime.UtcNow - participant.JoinedAt.Value).TotalSeconds;
                }
            }

            await context.SaveChangesAsync();

            logger.LogInformation("Meeting {MeetingId} ended by host {UserId}", meetingId, hostUserId);

            return meeting;
        }

        public async Task LeaveMeetingAsync(int meetingId, int userId)
        {
            var participant = await context.MeetingParticipants
                .FirstOrDefaultAsync(p => p.MeetingId == meetingId && p.UserId == userId);

            if (participant == null)
            {
                throw new ArgumentException("User is not a participant in this meeting");
            }

            participant.Status = ConnectionStatus.Left;
            participant.LeftAt = DateTime.UtcNow;
            participant.UpdatedAt = DateTime.UtcNow;

            if (participant.JoinedAt.HasValue)
            {
                participant.DurationSeconds = (int)(DateTime.UtcNow - participant.JoinedAt.Value).TotalSeconds;
            }

            await context.SaveChangesAsync();

            logger.LogInformation("User {UserId} left meeting {MeetingId}", userId, meetingId);
        }

        public async Task<MeetingParticipant> UpdateParticipantPermissionsAsync(int meetingId, int participantUserId, UpdateParticipantPermissionsRequest request, int requestingUserId)
        {
            var requestingParticipant = await context.MeetingParticipants
                .FirstOrDefaultAsync(p => p.MeetingId == meetingId && p.UserId == requestingUserId);

            if (requestingParticipant == null ||
                (requestingParticipant.Role != ParticipantRole.Host && requestingParticipant.Role != ParticipantRole.CoHost))
            {
                throw new UnauthorizedAccessException("Only hosts and co-hosts can update participant permissions");
            }

            var participant = await context.MeetingParticipants
                .FirstOrDefaultAsync(p => p.MeetingId == meetingId && p.UserId == participantUserId);

            if (participant == null)
            {
                throw new ArgumentException("Participant not found in this meeting");
            }

            // Update permissions
            if (request.Role.HasValue)
                participant.Role = request.Role.Value;

            if (request.CanShareScreen.HasValue)
                participant.CanShareScreen = request.CanShareScreen.Value;

            if (request.CanUseChat.HasValue)
                participant.CanUseChat = request.CanUseChat.Value;

            if (request.CanUnmute.HasValue)
                participant.CanUnmute = request.CanUnmute.Value;

            if (request.CanTurnOnCamera.HasValue)
                participant.CanTurnOnCamera = request.CanTurnOnCamera.Value;

            participant.UpdatedAt = DateTime.UtcNow;

            await context.SaveChangesAsync();

            logger.LogInformation("Permissions updated for user {UserId} in meeting {MeetingId} by {RequestingUserId}",
                participantUserId, meetingId, requestingUserId);

            return participant;
        }

        public async Task RemoveParticipantAsync(int meetingId, int participantUserId, int requestingUserId)
        {
            var requestingParticipant = await context.MeetingParticipants
                .FirstOrDefaultAsync(p => p.MeetingId == meetingId && p.UserId == requestingUserId);

            if (requestingParticipant == null ||
                (requestingParticipant.Role != ParticipantRole.Host && requestingParticipant.Role != ParticipantRole.CoHost))
            {
                throw new UnauthorizedAccessException("Only hosts and co-hosts can remove participants");
            }

            var participant = await context.MeetingParticipants
                .FirstOrDefaultAsync(p => p.MeetingId == meetingId && p.UserId == participantUserId);

            if (participant == null)
            {
                throw new ArgumentException("Participant not found in this meeting");
            }

            // Cannot remove the host
            if (participant.Role == ParticipantRole.Host)
            {
                throw new InvalidOperationException("Cannot remove the meeting host");
            }

            context.MeetingParticipants.Remove(participant);
            await context.SaveChangesAsync();

            logger.LogInformation("User {UserId} removed from meeting {MeetingId} by {RequestingUserId}",
                participantUserId, meetingId, requestingUserId);
        }

        public async Task<IEnumerable<MeetingParticipant>> GetMeetingParticipantsAsync(int meetingId, int requestingUserId)
        {
            if (!await CanUserAccessMeetingAsync(meetingId, requestingUserId))
            {
                throw new UnauthorizedAccessException("User does not have access to this meeting");
            }

            return await context.MeetingParticipants
                .Where(p => p.MeetingId == meetingId)
                .Include(p => p.User)
                .OrderBy(p => p.Role)
                .ThenBy(p => p.JoinedAt)
                .ToListAsync();
        }

        public async Task<string> GenerateUniqueMeetingCodeAsync()
        {
            string code;
            bool exists;

            do
            {
                var random = new Random();
                code = $"{random.Next(100, 999)}-{random.Next(100, 999)}-{random.Next(100, 999)}";
                exists = await context.Meetings.AnyAsync(m => m.MeetingCode == code);
            }
            while (exists);

            return code;
        }

        public async Task<bool> CanUserAccessMeetingAsync(int meetingId, int userId)
        {
            var meeting = await context.Meetings
                .Include(m => m.Participants)
                .FirstOrDefaultAsync(m => m.Id == meetingId);

            if (meeting == null)
            {
                return false;
            }

            // Host always has access
            if (meeting.HostUserId == userId)
            {
                return true;
            }

            // Check if user is a participant
            return meeting.Participants.Any(p => p.UserId == userId);
        }

        private string GenerateMeetingCode()
        {
            var random = new Random();
            return $"{random.Next(100, 999)}-{random.Next(100, 999)}-{random.Next(100, 999)}";
        }

        private string GenerateInvitationLink()
        {
            return $"https://meet.gmcadiom.com/join/{Guid.NewGuid()}";
        }
    }
}
