using GMCadiomMeeting.Shared.Enums;
using GMCadiomMeeting.Shared.Models;

namespace GMCadiomMeeting.Shared.ViewModels.Participant.GetInvitations;

/// <summary>
/// Request model for getting invitations with filters
/// </summary>
public class GetInvitationsRequest : PagedRequest
{
    /// <summary>
    /// Filter by invitation status
    /// </summary>
    public InvitationStatus? Status { get; set; }

    /// <summary>
    /// Filter by meeting ID
    /// </summary>
    public int? MeetingId { get; set; }

    /// <summary>
    /// Filter by sent date range start
    /// </summary>
    public DateTime? SentAfter { get; set; }

    /// <summary>
    /// Filter by sent date range end
    /// </summary>
    public DateTime? SentBefore { get; set; }

    /// <summary>
    /// Include only invitations sent by the current user
    /// </summary>
    public bool? SentByCurrentUser { get; set; }

    /// <summary>
    /// Include only invitations received by the current user
    /// </summary>
    public bool? ReceivedByCurrentUser { get; set; }
}
