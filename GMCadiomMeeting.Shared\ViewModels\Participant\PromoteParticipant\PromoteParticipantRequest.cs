using GMCadiomMeeting.Shared.Enums;
using System.ComponentModel.DataAnnotations;

namespace GMCadiomMeeting.Shared.ViewModels.Participant.PromoteParticipant;

/// <summary>
/// Request model for promoting a participant
/// </summary>
public class PromoteParticipantRequest
{
    /// <summary>
    /// Meeting ID
    /// </summary>
    [Required(ErrorMessage = "Meeting ID is required")]
    public int MeetingId { get; set; }

    /// <summary>
    /// Participant ID to promote
    /// </summary>
    [Required(ErrorMessage = "Participant ID is required")]
    public int ParticipantId { get; set; }

    /// <summary>
    /// New role for the participant
    /// </summary>
    [Required(ErrorMessage = "New role is required")]
    public ParticipantRole NewRole { get; set; }
}
