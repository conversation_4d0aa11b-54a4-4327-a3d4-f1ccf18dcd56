using GMCadiomMeeting.Shared.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GMCadiomMeeting.Data.Models;

/// <summary>
/// Represents a participant's involvement in a specific meeting
/// Junction table between Users and Meetings with additional participant-specific data
/// </summary>
[Table("MeetingParticipants")]
public class MeetingParticipant
{
    /// <summary>
    /// Unique identifier for the meeting participation record
    /// </summary>
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    /// <summary>
    /// Role of the participant in this meeting
    /// </summary>
    [Required]
    public ParticipantRole Role { get; set; } = ParticipantRole.Attendee;

    /// <summary>
    /// Current connection status of the participant
    /// </summary>
    [Required]
    public ConnectionStatus Status { get; set; } = ConnectionStatus.Disconnected;

    /// <summary>
    /// When the participant joined the meeting
    /// </summary>
    public DateTime? JoinedAt { get; set; }

    /// <summary>
    /// When the participant left the meeting
    /// </summary>
    public DateTime? LeftAt { get; set; }

    /// <summary>
    /// Total duration the participant was in the meeting (in seconds)
    /// </summary>
    public int? DurationSeconds { get; set; }

    /// <summary>
    /// Whether the participant's camera is enabled
    /// </summary>
    [Required]
    public bool IsCameraEnabled { get; set; } = false;

    /// <summary>
    /// Whether the participant's microphone is enabled
    /// </summary>
    [Required]
    public bool IsMicrophoneEnabled { get; set; } = false;

    /// <summary>
    /// Whether the participant is currently sharing their screen
    /// </summary>
    [Required]
    public bool IsScreenSharing { get; set; } = false;

    /// <summary>
    /// Whether the participant has permission to share screen
    /// </summary>
    [Required]
    public bool CanShareScreen { get; set; } = true;

    /// <summary>
    /// Whether the participant has permission to use chat
    /// </summary>
    [Required]
    public bool CanUseChat { get; set; } = true;

    /// <summary>
    /// Whether the participant has permission to unmute themselves
    /// </summary>
    [Required]
    public bool CanUnmute { get; set; } = true;

    /// <summary>
    /// Whether the participant has permission to turn on their camera
    /// </summary>
    [Required]
    public bool CanTurnOnCamera { get; set; } = true;

    /// <summary>
    /// Display name used by the participant in this meeting (can be different from user's display name)
    /// </summary>
    [StringLength(100)]
    [Column(TypeName = "varchar(100)")]
    public string? DisplayNameInMeeting { get; set; }

    /// <summary>
    /// IP address of the participant's connection
    /// </summary>
    [StringLength(45)]
    [Column(TypeName = "varchar(45)")]
    public string? IpAddress { get; set; }

    /// <summary>
    /// User agent string of the participant's browser/client
    /// </summary>
    [StringLength(500)]
    [Column(TypeName = "varchar(500)")]
    public string? UserAgent { get; set; }

    /// <summary>
    /// Connection quality metrics as JSON
    /// </summary>
    [Column(TypeName = "json")]
    public string? ConnectionMetrics { get; set; }

    /// <summary>
    /// When this participation record was created
    /// </summary>
    [Required]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// When this participation record was last updated
    /// </summary>
    [Required]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Foreign Keys

    /// <summary>
    /// ID of the user participating in the meeting
    /// </summary>
    [Required]
    public int UserId { get; set; }

    /// <summary>
    /// ID of the meeting the user is participating in
    /// </summary>
    [Required]
    public int MeetingId { get; set; }

    // Navigation Properties

    /// <summary>
    /// The user participating in the meeting
    /// </summary>
    [ForeignKey(nameof(UserId))]
    public virtual User User { get; set; } = null!;

    /// <summary>
    /// The meeting the user is participating in
    /// </summary>
    [ForeignKey(nameof(MeetingId))]
    public virtual Meeting Meeting { get; set; } = null!;
}
