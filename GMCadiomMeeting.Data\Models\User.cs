using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GMCadiomMeeting.Data.Models;

/// <summary>
/// Represents a user in the video conferencing system
/// </summary>
[Table("Users")]
public class User
{
    /// <summary>
    /// Unique identifier for the user
    /// </summary>
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    /// <summary>
    /// User's email address (used for authentication and invitations)
    /// </summary>
    [Required]
    [EmailAddress]
    [StringLength(255)]
    [Column(TypeName = "varchar(255)")]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// User's display name
    /// </summary>
    [Required]
    [StringLength(100)]
    [Column(TypeName = "varchar(100)")]
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// User's first name
    /// </summary>
    [StringLength(50)]
    [Column(TypeName = "varchar(50)")]
    public string? FirstName { get; set; }

    /// <summary>
    /// User's last name
    /// </summary>
    [StringLength(50)]
    [Column(TypeName = "varchar(50)")]
    public string? LastName { get; set; }

    /// <summary>
    /// Hashed password for authentication
    /// </summary>
    [Required]
    [StringLength(255)]
    [Column(TypeName = "varchar(255)")]
    public string PasswordHash { get; set; } = string.Empty;

    /// <summary>
    /// Salt used for password hashing
    /// </summary>
    [Required]
    [StringLength(255)]
    [Column(TypeName = "varchar(255)")]
    public string PasswordSalt { get; set; } = string.Empty;

    /// <summary>
    /// URL to user's profile picture
    /// </summary>
    [StringLength(500)]
    [Column(TypeName = "varchar(500)")]
    public string? ProfilePictureUrl { get; set; }

    /// <summary>
    /// User's timezone (e.g., "America/New_York")
    /// </summary>
    [StringLength(50)]
    [Column(TypeName = "varchar(50)")]
    public string? TimeZone { get; set; }

    /// <summary>
    /// Whether the user account is active
    /// </summary>
    [Required]
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Whether the user's email has been verified
    /// </summary>
    [Required]
    public bool IsEmailVerified { get; set; } = false;

    /// <summary>
    /// When the user account was created
    /// </summary>
    [Required]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// When the user account was last updated
    /// </summary>
    [Required]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// When the user last logged in
    /// </summary>
    public DateTime? LastLoginAt { get; set; }

    // Navigation Properties

    /// <summary>
    /// Meetings hosted by this user
    /// </summary>
    public virtual ICollection<Meeting> HostedMeetings { get; set; } = new List<Meeting>();

    /// <summary>
    /// Meeting participations for this user
    /// </summary>
    public virtual ICollection<MeetingParticipant> MeetingParticipations { get; set; } = new List<MeetingParticipant>();

    /// <summary>
    /// Invitations sent by this user
    /// </summary>
    public virtual ICollection<Invitation> SentInvitations { get; set; } = new List<Invitation>();

    /// <summary>
    /// Invitations received by this user
    /// </summary>
    public virtual ICollection<Invitation> ReceivedInvitations { get; set; } = new List<Invitation>();

    /// <summary>
    /// Chat messages sent by this user
    /// </summary>
    public virtual ICollection<ChatMessage> ChatMessages { get; set; } = new List<ChatMessage>();

    /// <summary>
    /// Screen sharing sessions initiated by this user
    /// </summary>
    public virtual ICollection<ScreenSharingMetadata> ScreenSharingSessions { get; set; } = new List<ScreenSharingMetadata>();
}
