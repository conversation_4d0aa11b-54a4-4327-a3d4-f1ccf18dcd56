using GMCadiomMeeting.Data.Models;
using GMCadiomMeeting.Services.MeetingServices;
using GMCadiomMeeting.Shared.ViewModels.Meeting;
using GMCadiomMeeting.Shared.ViewModels.Meeting.CreateMeeting;
using GMCadiomMeeting.Shared.ViewModels.Meeting.JoinMeeting;
using Microsoft.AspNetCore.Mvc;

namespace GMCadiomMeeting.Api.Controllers;

/// <summary>
/// Sample controller demonstrating how to use the Meeting entities
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class MeetingsController(MeetingService meetingService, ILogger<MeetingsController> logger) : ControllerBase
{
    /// <summary>
    /// Get all meetings for a specific user
    /// </summary>
    [HttpGet("user/{userId}")]
    public async Task<ActionResult<IEnumerable<MeetingDto>>> GetUserMeetings(int userId)
    {
        try
        {
            var meetings = await meetingService.GetUserMeetingsAsync(userId);
            return Ok(meetings);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving meetings for user {UserId}", userId);
            return StatusCode(500, "An error occurred while retrieving meetings");
        }
    }

    /// <summary>
    /// Get a specific meeting by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<MeetingDto>> GetMeeting(int id)
    {
        try
        {
            var meeting = await meetingService.GetMeeting(id);
            return Ok(meeting);
        }
        catch (KeyNotFoundException ex)
        {
            logger.LogWarning(ex, "Meeting not found {MeetingId}", id);
            return NotFound($"Meeting with ID {id} not found");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving meeting {MeetingId}", id);
            return StatusCode(500, "An error occurred while retrieving the meeting");
        }
    }

    /// <summary>
    /// Create a new meeting
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<Meeting>> CreateMeeting(CreateMeetingRequest request)
    {
        try
        {
            var createdMeeting = await meetingService.CreateMeetingAsync(request);
            return CreatedAtAction(nameof(GetMeeting), new { id = createdMeeting.Id }, createdMeeting);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error creating meeting");
            return StatusCode(500, "An error occurred while creating the meeting");
        }
    }

    /// <summary>
    /// Join a meeting by meeting code
    /// </summary>
    [HttpPost("{meetingCode}/join")]
    public async Task<ActionResult<MeetingParticipant>> JoinMeeting(string meetingCode, JoinMeetingRequest request)
    {
        try
        {
            var participant = await meetingService.JoinMeetingAsync(meetingCode, request);
            return Ok(participant);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error joining meeting {MeetingCode}", meetingCode);
            return StatusCode(500, "An error occurred while joining the meeting");
        }
    }
}
