using GMCadiomMeeting.Shared.Enums;
using System.ComponentModel.DataAnnotations;

namespace GMCadiomMeeting.Shared.ViewModels.Participant.UpdateParticipant;

/// <summary>
/// Request model for updating participant settings
/// </summary>
public class UpdateParticipantRequest
{
    /// <summary>
    /// Participant ID
    /// </summary>
    [Required(ErrorMessage = "Participant ID is required")]
    public int ParticipantId { get; set; }

    /// <summary>
    /// Display name in the meeting
    /// </summary>
    [StringLength(100, ErrorMessage = "Display name cannot exceed 100 characters")]
    public string? DisplayNameInMeeting { get; set; }

    /// <summary>
    /// Participant role in the meeting
    /// </summary>
    public ParticipantRole? Role { get; set; }

    /// <summary>
    /// Indicates if camera is enabled
    /// </summary>
    public bool? IsCameraEnabled { get; set; }

    /// <summary>
    /// Indicates if microphone is enabled
    /// </summary>
    public bool? IsMicrophoneEnabled { get; set; }
}
