﻿// <auto-generated />
using System;
using GMCadiomMeeting.Data.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace GMCadiomMeeting.Data.Migrations
{
    [DbContext(typeof(GMCadiomMeetingDbContext))]
    [Migration("20250707191855_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            MySqlModelBuilderExtensions.AutoIncrementColumns(modelBuilder);

            modelBuilder.Entity("GMCadiomMeeting.Data.Models.ChatMessage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("text");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("EditedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("FileMimeType")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("FileName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<long?>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("FileUrl")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsEdited")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<int>("MeetingId")
                        .HasColumnType("int");

                    b.Property<string>("Metadata")
                        .HasColumnType("json");

                    b.Property<string>("OriginalContent")
                        .HasMaxLength(2000)
                        .HasColumnType("text");

                    b.Property<int?>("RecipientId")
                        .HasColumnType("int");

                    b.Property<int?>("ReplyToMessageId")
                        .HasColumnType("int");

                    b.Property<int>("Scope")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<int>("SenderId")
                        .HasColumnType("int");

                    b.Property<DateTime>("SentAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ThumbnailUrl")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<int>("Type")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.HasKey("Id");

                    b.HasIndex("RecipientId");

                    b.HasIndex("ReplyToMessageId");

                    b.HasIndex("SenderId");

                    b.HasIndex("MeetingId", "Scope");

                    b.HasIndex("MeetingId", "SentAt");

                    b.ToTable("ChatMessages");
                });

            modelBuilder.Entity("GMCadiomMeeting.Data.Models.Invitation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("AllowJoinBeforeHost")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("ExpiresAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("InvitationToken")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<int>("InvitedRole")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(3);

                    b.Property<int?>("InvitedUserId")
                        .HasColumnType("int");

                    b.Property<string>("InviteeEmail")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("InviteeName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("LastResentAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("MeetingId")
                        .HasColumnType("int");

                    b.Property<int>("Method")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<string>("PersonalMessage")
                        .HasMaxLength(500)
                        .HasColumnType("text");

                    b.Property<int>("ResendCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<DateTime?>("RespondedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("SentAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("SentByUserId")
                        .HasColumnType("int");

                    b.Property<string>("Settings")
                        .HasColumnType("json");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("InvitationToken")
                        .IsUnique();

                    b.HasIndex("InvitedUserId");

                    b.HasIndex("InviteeEmail");

                    b.HasIndex("SentByUserId");

                    b.HasIndex("Status");

                    b.HasIndex("MeetingId", "Status");

                    b.ToTable("Invitations");
                });

            modelBuilder.Entity("GMCadiomMeeting.Data.Models.Meeting", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("ActualEndTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("ActualStartTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("text");

                    b.Property<int>("HostUserId")
                        .HasColumnType("int");

                    b.Property<string>("InvitationLink")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("IsRecordingEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsRecurring")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<int>("MaxParticipants")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(100);

                    b.Property<string>("MeetingCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("Password")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RecordingUrl")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<string>("RecurrencePattern")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime>("ScheduledEndTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("ScheduledStartTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Settings")
                        .HasColumnType("json");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<int>("Type")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("InvitationLink")
                        .IsUnique();

                    b.HasIndex("MeetingCode")
                        .IsUnique();

                    b.HasIndex("ScheduledStartTime");

                    b.HasIndex("Status");

                    b.HasIndex("HostUserId", "Status");

                    b.ToTable("Meetings");
                });

            modelBuilder.Entity("GMCadiomMeeting.Data.Models.MeetingParticipant", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("CanShareScreen")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(true);

                    b.Property<bool>("CanTurnOnCamera")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(true);

                    b.Property<bool>("CanUnmute")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(true);

                    b.Property<bool>("CanUseChat")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(true);

                    b.Property<string>("ConnectionMetrics")
                        .HasColumnType("json");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("DisplayNameInMeeting")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<int?>("DurationSeconds")
                        .HasColumnType("int");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("varchar(45)");

                    b.Property<bool>("IsCameraEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsMicrophoneEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsScreenSharing")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("JoinedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("LeftAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("MeetingId")
                        .HasColumnType("int");

                    b.Property<int>("Role")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(3);

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("Status");

                    b.HasIndex("MeetingId", "Status");

                    b.HasIndex("UserId", "MeetingId")
                        .IsUnique();

                    b.ToTable("MeetingParticipants");
                });

            modelBuilder.Entity("GMCadiomMeeting.Data.Models.ScreenSharingMetadata", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("AllowControlRequests")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<string>("ApplicationName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<int?>("BandwidthKbps")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int?>("DurationSeconds")
                        .HasColumnType("int");

                    b.Property<string>("EndReason")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime?>("EndedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int?>("FrameRate")
                        .HasColumnType("int");

                    b.Property<bool>("HasControlPermission")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsAudioShared")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsRecorded")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<int>("MeetingId")
                        .HasColumnType("int");

                    b.Property<string>("PerformanceMetrics")
                        .HasColumnType("json");

                    b.Property<int>("Quality")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<string>("RecordingUrl")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<string>("Resolution")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("SessionId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("SharedContentTitle")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<int>("SharingUserId")
                        .HasColumnType("int");

                    b.Property<DateTime>("StartedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<string>("TechnicalMetadata")
                        .HasColumnType("json");

                    b.Property<int>("Type")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("SessionId")
                        .IsUnique();

                    b.HasIndex("SharingUserId");

                    b.HasIndex("StartedAt");

                    b.HasIndex("MeetingId", "Status");

                    b.ToTable("ScreenSharingMetadata");
                });

            modelBuilder.Entity("GMCadiomMeeting.Data.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("FirstName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsEmailVerified")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("LastName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("PasswordSalt")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ProfilePictureUrl")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<string>("TimeZone")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.HasIndex("IsActive");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("GMCadiomMeeting.Data.Models.ChatMessage", b =>
                {
                    b.HasOne("GMCadiomMeeting.Data.Models.Meeting", "Meeting")
                        .WithMany("ChatMessages")
                        .HasForeignKey("MeetingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("GMCadiomMeeting.Data.Models.User", "Recipient")
                        .WithMany()
                        .HasForeignKey("RecipientId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("GMCadiomMeeting.Data.Models.ChatMessage", "ReplyToMessage")
                        .WithMany("Replies")
                        .HasForeignKey("ReplyToMessageId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("GMCadiomMeeting.Data.Models.User", "Sender")
                        .WithMany("ChatMessages")
                        .HasForeignKey("SenderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Meeting");

                    b.Navigation("Recipient");

                    b.Navigation("ReplyToMessage");

                    b.Navigation("Sender");
                });

            modelBuilder.Entity("GMCadiomMeeting.Data.Models.Invitation", b =>
                {
                    b.HasOne("GMCadiomMeeting.Data.Models.User", "InvitedUser")
                        .WithMany("ReceivedInvitations")
                        .HasForeignKey("InvitedUserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("GMCadiomMeeting.Data.Models.Meeting", "Meeting")
                        .WithMany("Invitations")
                        .HasForeignKey("MeetingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("GMCadiomMeeting.Data.Models.User", "SentByUser")
                        .WithMany("SentInvitations")
                        .HasForeignKey("SentByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("InvitedUser");

                    b.Navigation("Meeting");

                    b.Navigation("SentByUser");
                });

            modelBuilder.Entity("GMCadiomMeeting.Data.Models.Meeting", b =>
                {
                    b.HasOne("GMCadiomMeeting.Data.Models.User", "HostUser")
                        .WithMany("HostedMeetings")
                        .HasForeignKey("HostUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("HostUser");
                });

            modelBuilder.Entity("GMCadiomMeeting.Data.Models.MeetingParticipant", b =>
                {
                    b.HasOne("GMCadiomMeeting.Data.Models.Meeting", "Meeting")
                        .WithMany("Participants")
                        .HasForeignKey("MeetingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("GMCadiomMeeting.Data.Models.User", "User")
                        .WithMany("MeetingParticipations")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Meeting");

                    b.Navigation("User");
                });

            modelBuilder.Entity("GMCadiomMeeting.Data.Models.ScreenSharingMetadata", b =>
                {
                    b.HasOne("GMCadiomMeeting.Data.Models.Meeting", "Meeting")
                        .WithMany("ScreenSharingSessions")
                        .HasForeignKey("MeetingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("GMCadiomMeeting.Data.Models.User", "SharingUser")
                        .WithMany("ScreenSharingSessions")
                        .HasForeignKey("SharingUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Meeting");

                    b.Navigation("SharingUser");
                });

            modelBuilder.Entity("GMCadiomMeeting.Data.Models.ChatMessage", b =>
                {
                    b.Navigation("Replies");
                });

            modelBuilder.Entity("GMCadiomMeeting.Data.Models.Meeting", b =>
                {
                    b.Navigation("ChatMessages");

                    b.Navigation("Invitations");

                    b.Navigation("Participants");

                    b.Navigation("ScreenSharingSessions");
                });

            modelBuilder.Entity("GMCadiomMeeting.Data.Models.User", b =>
                {
                    b.Navigation("ChatMessages");

                    b.Navigation("HostedMeetings");

                    b.Navigation("MeetingParticipations");

                    b.Navigation("ReceivedInvitations");

                    b.Navigation("ScreenSharingSessions");

                    b.Navigation("SentInvitations");
                });
#pragma warning restore 612, 618
        }
    }
}
