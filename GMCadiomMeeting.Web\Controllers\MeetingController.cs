using GMCadiomMeeting.Shared.ViewModels.Meeting.CreateMeeting;
using GMCadiomMeeting.Shared.ViewModels.Meeting.JoinMeeting;
using GMCadiomMeeting.Shared.ViewModels.Participant.SendInvitation;
using GMCadiomMeeting.Web.Services;
using Microsoft.AspNetCore.Mvc;

namespace GMCadiomMeeting.Web.Controllers;

public class MeetingController : Controller
{
    private readonly IAuthService _authService;
    private readonly IApiService _apiService;
    private readonly ILogger<MeetingController> _logger;

    public MeetingController(IAuthService authService, IApiService apiService, ILogger<MeetingController> logger)
    {
        _authService = authService;
        _apiService = apiService;
        _logger = logger;
    }

    [HttpGet]
    public async Task<IActionResult> Index()
    {
        if (!_authService.IsAuthenticated())
        {
            return RedirectToAction("Login", "Account");
        }

        var userId = _authService.GetCurrentUserId();
        if (!userId.HasValue)
        {
            return RedirectToAction("Login", "Account");
        }

        var meetings = await _apiService.GetUserMeetingsAsync(userId.Value);
        return View(meetings);
    }

    [HttpGet]
    public IActionResult Create()
    {
        if (!_authService.IsAuthenticated())
        {
            return RedirectToAction("Login", "Account");
        }

        var request = new CreateMeetingRequest
        {
            ScheduledStartTime = DateTime.Now.AddHours(1),
            ScheduledEndTime = DateTime.Now.AddHours(2)
        };

        return View(request);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(CreateMeetingRequest request)
    {
        if (!_authService.IsAuthenticated())
        {
            return RedirectToAction("Login", "Account");
        }

        var userId = _authService.GetCurrentUserId();
        if (!userId.HasValue)
        {
            return RedirectToAction("Login", "Account");
        }

        if (!ModelState.IsValid)
        {
            return View(request);
        }

        if (request.ScheduledStartTime >= request.ScheduledEndTime)
        {
            ModelState.AddModelError(nameof(request.ScheduledEndTime), "End time must be after start time.");
            return View(request);
        }

        if (request.ScheduledStartTime < DateTime.Now.AddMinutes(-5))
        {
            ModelState.AddModelError(nameof(request.ScheduledStartTime), "Cannot schedule meetings in the past.");
            return View(request);
        }

        var meeting = await _apiService.CreateMeetingAsync(request, userId.Value);

        if (meeting != null)
        {
            _logger.LogInformation("Meeting {MeetingId} created by user {UserId}", meeting.Id, userId.Value);
            TempData["SuccessMessage"] = "Meeting created successfully!";
            return RedirectToAction("Details", new { id = meeting.Id });
        }

        ModelState.AddModelError(string.Empty, "Failed to create meeting. Please try again.");
        return View(request);
    }

    [HttpGet]
    public async Task<IActionResult> Details(int id)
    {
        if (!_authService.IsAuthenticated())
        {
            return RedirectToAction("Login", "Account");
        }

        var meeting = await _apiService.GetMeetingAsync(id);
        if (meeting == null)
        {
            return NotFound();
        }

        var userId = _authService.GetCurrentUserId();
        var isParticipant = meeting.HostUserId == userId ||
                           meeting.Participants.Any(p => p.UserId == userId);

        if (!isParticipant)
        {
            return Forbid();
        }

        return View(meeting);
    }

    [HttpGet]
    public IActionResult Join()
    {
        var model = new JoinMeetingRequest();
        return View(model);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Join(JoinMeetingRequest model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        var userId = _authService.GetCurrentUserId();
        if (!userId.HasValue)
        {
            // For guest users, we'll need to handle this differently
            // For now, redirect to login
            return RedirectToAction("Login", "Account");
        }

        model.UserId = userId.Value;
        var participant = await _apiService.JoinMeetingAsync(model);

        if (participant != null)
        {
            _logger.LogInformation("User {UserId} joined meeting {MeetingCode}", userId.Value, model.MeetingCode);
            return RedirectToAction("Room", new { id = participant.MeetingId });
        }

        ModelState.AddModelError(string.Empty, "Failed to join meeting. Please check the meeting code and try again.");
        return View(model);
    }

    [HttpGet]
    public async Task<IActionResult> Room(int id)
    {
        if (!_authService.IsAuthenticated())
        {
            return RedirectToAction("Login", "Account");
        }

        var meeting = await _apiService.GetMeetingAsync(id);
        if (meeting == null)
        {
            return NotFound();
        }

        var userId = _authService.GetCurrentUserId();
        var participant = meeting.Participants.FirstOrDefault(p => p.UserId == userId);

        if (participant == null)
        {
            return Forbid();
        }

        var user = await _authService.GetCurrentUserAsync();
        ViewBag.User = user;
        ViewBag.Participant = participant;

        return View(meeting);
    }

    [HttpGet]
    public async Task<IActionResult> Invitations(int id)
    {
        if (!_authService.IsAuthenticated())
        {
            return RedirectToAction("Login", "Account");
        }

        var meeting = await _apiService.GetMeetingAsync(id);
        if (meeting == null)
        {
            return NotFound();
        }

        var userId = _authService.GetCurrentUserId();
        if (meeting.HostUserId != userId)
        {
            return Forbid();
        }

        var invitations = await _apiService.GetMeetingInvitationsAsync(id);

        ViewBag.Meeting = meeting;
        return View(invitations);
    }

    [HttpGet]
    public async Task<IActionResult> SendInvitation(int id)
    {
        if (!_authService.IsAuthenticated())
        {
            return RedirectToAction("Login", "Account");
        }

        var meeting = await _apiService.GetMeetingAsync(id);
        if (meeting == null)
        {
            return NotFound();
        }

        var userId = _authService.GetCurrentUserId();
        if (meeting.HostUserId != userId)
        {
            return Forbid();
        }

        var request = new SendInvitationRequest
        {
            MeetingId = id,
            ExpiresAt = meeting.ScheduledStartTime.AddHours(-1)
        };

        ViewBag.Meeting = meeting;
        return View(request);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> SendInvitation(SendInvitationRequest request)
    {
        if (!_authService.IsAuthenticated())
        {
            return RedirectToAction("Login", "Account");
        }

        var userId = _authService.GetCurrentUserId();
        if (!userId.HasValue)
        {
            return RedirectToAction("Login", "Account");
        }

        if (!ModelState.IsValid)
        {
            var meeting = await _apiService.GetMeetingAsync(request.MeetingId);
            ViewBag.Meeting = meeting;
            return View(request);
        }

        var result = await _apiService.SendInvitationsAsync(request, userId.Value);

        if (result)
        {
            TempData["SuccessMessage"] = "Invitations sent successfully!";
            return RedirectToAction("Invitations", new { id = request.MeetingId });
        }

        ModelState.AddModelError(string.Empty, "Failed to send invitations. Please try again.");
        var meetingForView = await _apiService.GetMeetingAsync(request.MeetingId);
        ViewBag.Meeting = meetingForView;
        return View(request);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> RespondToInvitation(int invitationId, bool accept)
    {
        if (!_authService.IsAuthenticated())
        {
            return RedirectToAction("Login", "Account");
        }

        var result = await _apiService.RespondToInvitationAsync(invitationId, accept);

        if (result)
        {
            var message = accept ? "Invitation accepted!" : "Invitation declined.";
            TempData["SuccessMessage"] = message;
        }
        else
        {
            TempData["ErrorMessage"] = "Failed to respond to invitation.";
        }

        return RedirectToAction("MyInvitations");
    }

    [HttpGet]
    public async Task<IActionResult> MyInvitations()
    {
        if (!_authService.IsAuthenticated())
        {
            return RedirectToAction("Login", "Account");
        }

        var userId = _authService.GetCurrentUserId();
        if (!userId.HasValue)
        {
            return RedirectToAction("Login", "Account");
        }

        var invitations = await _apiService.GetUserInvitationsAsync(userId.Value);
        return View(invitations);
    }
}
