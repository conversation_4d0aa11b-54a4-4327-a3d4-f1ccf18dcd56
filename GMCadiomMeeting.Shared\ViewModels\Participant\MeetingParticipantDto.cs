using GMCadiomMeeting.Shared.Enums;
using GMCadiomMeeting.Shared.ViewModels.User;

namespace GMCadiomMeeting.Shared.ViewModels.Participant;

/// <summary>
/// Meeting participant information for API responses
/// </summary>
public class MeetingParticipantDto
{
    /// <summary>
    /// Participant unique identifier
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Meeting ID
    /// </summary>
    public int MeetingId { get; set; }

    /// <summary>
    /// User ID (null for guest participants)
    /// </summary>
    public int? UserId { get; set; }

    /// <summary>
    /// User information (if registered user)
    /// </summary>
    public UserDto? User { get; set; }

    /// <summary>
    /// Display name in the meeting
    /// </summary>
    public string? DisplayNameInMeeting { get; set; }

    /// <summary>
    /// Participant role in the meeting
    /// </summary>
    public ParticipantRole Role { get; set; }

    /// <summary>
    /// Connection status
    /// </summary>
    public ConnectionStatus Status { get; set; }

    /// <summary>
    /// Indicates if camera is enabled
    /// </summary>
    public bool IsCameraEnabled { get; set; }

    /// <summary>
    /// Indicates if microphone is enabled
    /// </summary>
    public bool IsMicrophoneEnabled { get; set; }

    /// <summary>
    /// Indicates if participant is sharing screen
    /// </summary>
    public bool IsScreenSharing { get; set; }

    /// <summary>
    /// Time when participant joined the meeting
    /// </summary>
    public DateTime? JoinedAt { get; set; }

    /// <summary>
    /// Time when participant left the meeting
    /// </summary>
    public DateTime? LeftAt { get; set; }

    /// <summary>
    /// Total duration in the meeting (in minutes)
    /// </summary>
    public int? DurationMinutes { get; set; }

    /// <summary>
    /// Participant's IP address
    /// </summary>
    public string? IpAddress { get; set; }

    /// <summary>
    /// Participant's user agent
    /// </summary>
    public string? UserAgent { get; set; }

    /// <summary>
    /// Date when participant record was created
    /// </summary>
    public DateTime CreatedAt { get; set; }
}
