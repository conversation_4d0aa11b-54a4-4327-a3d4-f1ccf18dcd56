using GMCadiomMeeting.Shared.Enums;
using GMCadiomMeeting.Shared.Models;

namespace GMCadiomMeeting.Shared.ViewModels.Meeting.GetMeetings;

/// <summary>
/// Request model for getting meetings with filters
/// </summary>
public class GetMeetingsRequest : PagedRequest
{
    /// <summary>
    /// Filter by meeting status
    /// </summary>
    public MeetingStatus? Status { get; set; }

    /// <summary>
    /// Filter by meeting type
    /// </summary>
    public MeetingType? Type { get; set; }

    /// <summary>
    /// Filter by date range start
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// Filter by date range end
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Include only meetings where user is host
    /// </summary>
    public bool? HostedByUser { get; set; }

    /// <summary>
    /// Include only meetings where user is participant
    /// </summary>
    public bool? ParticipatedByUser { get; set; }
}
