# GMCadiom Meeting - Production Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the GMCadiom Meeting video conferencing application to production environments.

## Prerequisites

- .NET 9 Runtime
- MySQL 8.0+ Server
- Web server (IIS, Nginx, or Apache)
- SSL Certificate
- Domain name

## Production Environment Setup

### 1. Database Configuration

#### MySQL Server Setup
```sql
-- Create production database
CREATE DATABASE GMCadiomMeeting_Prod;

-- Create dedicated user
CREATE USER 'gmcadiom_prod'@'%' IDENTIFIED BY 'STRONG_PRODUCTION_PASSWORD';
GRANT ALL PRIVILEGES ON GMCadiomMeeting_Prod.* TO 'gmcadiom_prod'@'%';
FLUSH PRIVILEGES;

-- Configure for production
SET GLOBAL max_connections = 500;
SET GLOBAL innodb_buffer_pool_size = 2G;
```

#### Connection String
Update `appsettings.Production.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=prod-mysql-server;Database=GMCadiomMeeting_Prod;User=gmcadiom_prod;Password=STRONG_PRODUCTION_PASSWORD;SslMode=Required;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore": "Error"
    }
  },
  "AllowedHosts": "yourdomain.com,*.yourdomain.com"
}
```

### 2. Application Configuration

#### Environment Variables
```bash
export ASPNETCORE_ENVIRONMENT=Production
export ASPNETCORE_URLS="https://+:443;http://+:80"
export ASPNETCORE_HTTPS_PORT=443
```

#### Security Headers
Add to `Program.cs`:
```csharp
app.UseHsts();
app.UseHttpsRedirection();

app.Use(async (context, next) =>
{
    context.Response.Headers.Add("X-Frame-Options", "DENY");
    context.Response.Headers.Add("X-Content-Type-Options", "nosniff");
    context.Response.Headers.Add("X-XSS-Protection", "1; mode=block");
    context.Response.Headers.Add("Referrer-Policy", "strict-origin-when-cross-origin");
    await next();
});
```

### 3. Build and Publish

#### Build for Production
```bash
# Clean and restore
dotnet clean
dotnet restore

# Build in Release mode
dotnet build --configuration Release

# Publish application
dotnet publish --configuration Release --output ./publish --self-contained false
```

#### Docker Deployment (Optional)
Create `Dockerfile`:
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
COPY ["GMCadiomMeeting.Api/GMCadiomMeeting.Api.csproj", "GMCadiomMeeting.Api/"]
COPY ["GMCadiomMeeting.Data/GMCadiomMeeting.Data.csproj", "GMCadiomMeeting.Data/"]
RUN dotnet restore "GMCadiomMeeting.Api/GMCadiomMeeting.Api.csproj"
COPY . .
WORKDIR "/src/GMCadiomMeeting.Api"
RUN dotnet build "GMCadiomMeeting.Api.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "GMCadiomMeeting.Api.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "GMCadiomMeeting.Api.dll"]
```

### 4. Database Migration

#### Apply Migrations
```bash
# Generate migration script
dotnet ef migrations script --project GMCadiomMeeting.Data --output production-migration.sql

# Apply to production database
mysql -h prod-mysql-server -u gmcadiom_prod -p GMCadiomMeeting_Prod < production-migration.sql
```

### 5. Web Server Configuration

#### Nginx Configuration
```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;

    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection keep-alive;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # SignalR WebSocket support
    location /meetingHub {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

#### IIS Configuration (Windows)
Create `web.config`:
```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="dotnet" arguments=".\GMCadiomMeeting.Api.dll" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" hostingModel="inprocess" />
    </system.webServer>
  </location>
</configuration>
```

### 6. Monitoring and Logging

#### Application Insights (Azure)
```csharp
builder.Services.AddApplicationInsightsTelemetry();
```

#### Serilog Configuration
```csharp
builder.Host.UseSerilog((context, configuration) =>
    configuration.ReadFrom.Configuration(context.Configuration));
```

Add to `appsettings.Production.json`:
```json
{
  "Serilog": {
    "MinimumLevel": "Warning",
    "WriteTo": [
      {
        "Name": "File",
        "Args": {
          "path": "/var/log/gmcadiom/log-.txt",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 30
        }
      }
    ]
  }
}
```

### 7. Performance Optimization

#### Database Optimization
```sql
-- Add indexes for performance
CREATE INDEX IX_Meetings_HostUserId_Status ON Meetings(HostUserId, Status);
CREATE INDEX IX_MeetingParticipants_MeetingId_Status ON MeetingParticipants(MeetingId, Status);
CREATE INDEX IX_ChatMessages_MeetingId_SentAt ON ChatMessages(MeetingId, SentAt);

-- Configure MySQL for production
SET GLOBAL query_cache_size = 268435456;
SET GLOBAL query_cache_type = ON;
```

#### Application Performance
```csharp
// Add response compression
builder.Services.AddResponseCompression();

// Add memory caching
builder.Services.AddMemoryCache();

// Configure Entity Framework
builder.Services.AddDbContext<GMCadiomMeetingDbContext>(options =>
{
    options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString), 
        mysqlOptions =>
        {
            mysqlOptions.EnableRetryOnFailure(3);
            mysqlOptions.CommandTimeout(30);
        });
    options.EnableSensitiveDataLogging(false);
    options.EnableServiceProviderCaching();
});
```

### 8. Security Hardening

#### JWT Authentication (Production)
```csharp
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"],
            ValidAudience = builder.Configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"]))
        };
    });
```

#### CORS Configuration
```csharp
builder.Services.AddCors(options =>
{
    options.AddPolicy("Production", policy =>
    {
        policy.WithOrigins("https://yourdomain.com", "https://www.yourdomain.com")
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials();
    });
});
```

### 9. Health Checks

```csharp
builder.Services.AddHealthChecks()
    .AddDbContextCheck<GMCadiomMeetingDbContext>()
    .AddMySql(connectionString);

app.MapHealthChecks("/health");
```

### 10. Backup Strategy

#### Database Backup
```bash
#!/bin/bash
# Daily backup script
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -h prod-mysql-server -u gmcadiom_prod -p GMCadiomMeeting_Prod > /backups/gmcadiom_$DATE.sql
gzip /backups/gmcadiom_$DATE.sql

# Keep only last 30 days
find /backups -name "gmcadiom_*.sql.gz" -mtime +30 -delete
```

#### Application Backup
```bash
#!/bin/bash
# Application files backup
tar -czf /backups/app_$(date +%Y%m%d).tar.gz /var/www/gmcadiom/
```

### 11. SSL/TLS Configuration

#### Let's Encrypt (Free SSL)
```bash
# Install certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 12. Scaling Considerations

#### Load Balancing
For multiple servers, configure SignalR with Redis:
```csharp
builder.Services.AddSignalR().AddStackExchangeRedis("redis-connection-string");
```

#### Database Scaling
- Read replicas for read-heavy operations
- Connection pooling optimization
- Query optimization and indexing

### 13. Deployment Checklist

- [ ] Database server configured and secured
- [ ] Application built and published
- [ ] Database migrations applied
- [ ] Web server configured with SSL
- [ ] Environment variables set
- [ ] Logging configured
- [ ] Health checks working
- [ ] Backup strategy implemented
- [ ] Monitoring setup
- [ ] Security headers configured
- [ ] CORS properly configured
- [ ] Performance testing completed

### 14. Troubleshooting

#### Common Issues
1. **Database Connection**: Check connection string and firewall
2. **SignalR Issues**: Verify WebSocket support and proxy configuration
3. **SSL Problems**: Check certificate validity and configuration
4. **Performance**: Monitor database queries and connection pool

#### Log Analysis
```bash
# Check application logs
tail -f /var/log/gmcadiom/log-$(date +%Y%m%d).txt

# Check system logs
journalctl -u gmcadiom-api -f
```

This deployment guide provides a comprehensive approach to deploying the GMCadiom Meeting application in a production environment with proper security, performance, and monitoring considerations.
