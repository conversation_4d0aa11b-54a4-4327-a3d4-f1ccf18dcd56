using System.ComponentModel;
using System.Reflection;

namespace GMCadiomMeeting.Shared.Extensions;

/// <summary>
/// Extension methods for enums
/// </summary>
public static class EnumExtensions
{
    /// <summary>
    /// Gets the description attribute value for an enum value
    /// </summary>
    /// <param name="value">The enum value</param>
    /// <returns>Description or enum name if no description found</returns>
    public static string GetDescription(this Enum value)
    {
        var field = value.GetType().GetField(value.ToString());
        if (field == null) return value.ToString();

        var attribute = field.GetCustomAttribute<DescriptionAttribute>();
        return attribute?.Description ?? value.ToString();
    }

    /// <summary>
    /// Gets all enum values with their descriptions
    /// </summary>
    /// <typeparam name="T">Enum type</typeparam>
    /// <returns>Dictionary of enum values and descriptions</returns>
    public static Dictionary<T, string> GetEnumDescriptions<T>() where T : Enum
    {
        var result = new Dictionary<T, string>();
        var enumValues = Enum.GetValues(typeof(T)).Cast<T>();

        foreach (var value in enumValues)
        {
            result[value] = value.GetDescription();
        }

        return result;
    }

    /// <summary>
    /// Parses a string to enum value, ignoring case
    /// </summary>
    /// <typeparam name="T">Enum type</typeparam>
    /// <param name="value">String value to parse</param>
    /// <param name="defaultValue">Default value if parsing fails</param>
    /// <returns>Parsed enum value or default</returns>
    public static T ParseEnum<T>(this string value, T defaultValue = default) where T : struct, Enum
    {
        if (string.IsNullOrWhiteSpace(value))
            return defaultValue;

        return Enum.TryParse<T>(value, true, out var result) ? result : defaultValue;
    }

    /// <summary>
    /// Checks if an enum value is defined
    /// </summary>
    /// <typeparam name="T">Enum type</typeparam>
    /// <param name="value">Value to check</param>
    /// <returns>True if defined, false otherwise</returns>
    public static bool IsDefined<T>(this T value) where T : Enum
    {
        return Enum.IsDefined(typeof(T), value);
    }

    /// <summary>
    /// Gets all enum values as a list
    /// </summary>
    /// <typeparam name="T">Enum type</typeparam>
    /// <returns>List of all enum values</returns>
    public static List<T> GetAllValues<T>() where T : Enum
    {
        return Enum.GetValues(typeof(T)).Cast<T>().ToList();
    }

    /// <summary>
    /// Converts enum to select list items for dropdowns
    /// </summary>
    /// <typeparam name="T">Enum type</typeparam>
    /// <param name="selectedValue">Currently selected value</param>
    /// <returns>List of select list items</returns>
    public static List<SelectListItem> ToSelectList<T>(T? selectedValue = null) where T : struct, Enum
    {
        var items = new List<SelectListItem>();
        var enumValues = Enum.GetValues(typeof(T)).Cast<T>();

        foreach (var value in enumValues)
        {
            items.Add(new SelectListItem
            {
                Value = value.ToString(),
                Text = (value as Enum)?.GetDescription() ?? value.ToString(),
                Selected = selectedValue.HasValue && value.Equals(selectedValue.Value)
            });
        }

        return items;
    }
}

/// <summary>
/// Simple select list item for dropdown lists
/// </summary>
public class SelectListItem
{
    public string Value { get; set; } = string.Empty;
    public string Text { get; set; } = string.Empty;
    public bool Selected { get; set; }
}
