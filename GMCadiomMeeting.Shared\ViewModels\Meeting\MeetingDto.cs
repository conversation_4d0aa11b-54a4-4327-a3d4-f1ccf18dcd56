using GMCadiomMeeting.Shared.Enums;
using GMCadiomMeeting.Shared.ViewModels.Participant;
using GMCadiomMeeting.Shared.ViewModels.User;

namespace GMCadiomMeeting.Shared.ViewModels.Meeting;

/// <summary>
/// Meeting information for API responses
/// </summary>
public class MeetingDto
{
    /// <summary>
    /// Meeting unique identifier
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Meeting title
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Meeting description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Meeting type
    /// </summary>
    public MeetingType Type { get; set; }

    /// <summary>
    /// Meeting status
    /// </summary>
    public MeetingStatus Status { get; set; }

    /// <summary>
    /// Unique meeting code for joining
    /// </summary>
    public string MeetingCode { get; set; } = string.Empty;

    /// <summary>
    /// Meeting password (if required)
    /// </summary>
    public string? Password { get; set; }

    /// <summary>
    /// Maximum number of participants allowed
    /// </summary>
    public int MaxParticipants { get; set; }

    /// <summary>
    /// Scheduled start time
    /// </summary>
    public DateTime ScheduledStartTime { get; set; }

    /// <summary>
    /// Scheduled end time
    /// </summary>
    public DateTime ScheduledEndTime { get; set; }

    /// <summary>
    /// Actual start time (when meeting was started)
    /// </summary>
    public DateTime? ActualStartTime { get; set; }

    /// <summary>
    /// Actual end time (when meeting was ended)
    /// </summary>
    public DateTime? ActualEndTime { get; set; }

    /// <summary>
    /// Indicates if recording is enabled
    /// </summary>
    public bool IsRecordingEnabled { get; set; }

    /// <summary>
    /// URL to meeting recording (if available)
    /// </summary>
    public string? RecordingUrl { get; set; }

    /// <summary>
    /// Indicates if the meeting is recurring
    /// </summary>
    public bool IsRecurring { get; set; }

    /// <summary>
    /// Recurrence pattern (daily, weekly, monthly, etc.)
    /// </summary>
    public string? RecurrencePattern { get; set; }

    /// <summary>
    /// Meeting invitation link
    /// </summary>
    public string? InvitationLink { get; set; }

    /// <summary>
    /// Host user ID
    /// </summary>
    public int HostUserId { get; set; }

    /// <summary>
    /// Host user information
    /// </summary>
    public UserDto? HostUser { get; set; }

    /// <summary>
    /// List of meeting participants
    /// </summary>
    public List<MeetingParticipantDto> Participants { get; set; } = new();

    /// <summary>
    /// Date when the meeting was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Date when the meeting was last updated
    /// </summary>
    public DateTime? UpdatedAt { get; set; }
}
