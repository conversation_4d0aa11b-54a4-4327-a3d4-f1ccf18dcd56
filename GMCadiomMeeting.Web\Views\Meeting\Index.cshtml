@using GMCadiomMeeting.Shared.ViewModels.Meeting
@model IEnumerable<MeetingDto>
@{
    ViewData["Title"] = "My Meetings";
    var upcomingMeetings = Model.Where(m => m.Status == GMCadiomMeeting.Shared.Enums.MeetingStatus.Scheduled && m.ScheduledStartTime > DateTime.Now);
    var activeMeetings = Model.Where(m => m.Status == GMCadiomMeeting.Shared.Enums.MeetingStatus.InProgress);
    var pastMeetings = Model.Where(m => m.Status == GMCadiomMeeting.Shared.Enums.MeetingStatus.Ended || m.ScheduledStartTime < DateTime.Now);
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">My Meetings</h1>
                    <p class="text-muted mb-0">Manage your scheduled and past meetings</p>
                </div>
                <div class="d-flex gap-2">
                    <a asp-action="Create" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>New Meeting
                    </a>
                    <a asp-action="Join" class="btn btn-outline-primary">
                        <i class="fas fa-sign-in-alt me-2"></i>Join Meeting
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Meeting Stats -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold text-primary">@upcomingMeetings.Count()</div>
                            <div class="text-muted small">Upcoming</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fas fa-video"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold text-success">@activeMeetings.Count()</div>
                            <div class="text-muted small">Active</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fas fa-history"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold text-secondary">@pastMeetings.Count()</div>
                            <div class="text-muted small">Completed</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold text-info">@Model.Count()</div>
                            <div class="text-muted small">Total</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Meetings -->
    @if (activeMeetings.Any())
    {
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-success text-white py-3">
                        <h5 class="mb-0">
                            <i class="fas fa-video me-2"></i>Active Meetings
                        </h5>
                    </div>
                    <div class="card-body">
                        @foreach (var meeting in activeMeetings)
                        {
                            <div class="d-flex align-items-center justify-content-between p-3 border rounded mb-3">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">@meeting.Title</h6>
                                    <div class="text-muted small">
                                        <i class="fas fa-clock me-1"></i>Started @meeting.ActualStartTime?.ToString("h:mm tt")
                                        <span class="mx-2">•</span>
                                        <i class="fas fa-users me-1"></i>@meeting.Participants.Count participants
                                    </div>
                                </div>
                                <div class="flex-shrink-0">
                                    <a asp-action="Room" asp-route-id="@meeting.Id" class="btn btn-success">
                                        <i class="fas fa-video me-1"></i>Join
                                    </a>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Upcoming Meetings -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom py-3">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt text-primary me-2"></i>Upcoming Meetings
                    </h5>
                </div>
                <div class="card-body">
                    @if (upcomingMeetings.Any())
                    {
                        @foreach (var meeting in upcomingMeetings.OrderBy(m => m.ScheduledStartTime))
                        {
                            <div class="d-flex align-items-center justify-content-between p-3 border rounded mb-3">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-2">
                                        <h6 class="mb-0 me-2">@meeting.Title</h6>
                                        <span class="badge bg-@(meeting.Type == GMCadiomMeeting.Shared.Enums.MeetingType.OneOnOne ? "info" : 
                                                              meeting.Type == GMCadiomMeeting.Shared.Enums.MeetingType.Group ? "primary" : "warning")">
                                            @meeting.Type
                                        </span>
                                    </div>
                                    <div class="text-muted small">
                                        <i class="fas fa-calendar me-1"></i>@meeting.ScheduledStartTime.ToString("MMM dd, yyyy")
                                        <span class="mx-2">•</span>
                                        <i class="fas fa-clock me-1"></i>@meeting.ScheduledStartTime.ToString("h:mm tt") - @meeting.ScheduledEndTime.ToString("h:mm tt")
                                        <span class="mx-2">•</span>
                                        <i class="fas fa-key me-1"></i>@meeting.MeetingCode
                                    </div>
                                </div>
                                <div class="flex-shrink-0">
                                    <div class="btn-group">
                                        <a asp-action="Details" asp-route-id="@meeting.Id" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a asp-action="Room" asp-route-id="@meeting.Id" class="btn btn-primary btn-sm">
                                            <i class="fas fa-video"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <p class="text-muted mb-3">No upcoming meetings scheduled</p>
                            <a asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Schedule Your First Meeting
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Past Meetings -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom py-3">
                    <h5 class="mb-0">
                        <i class="fas fa-history text-secondary me-2"></i>Past Meetings
                    </h5>
                </div>
                <div class="card-body">
                    @if (pastMeetings.Any())
                    {
                        @foreach (var meeting in pastMeetings.OrderByDescending(m => m.ScheduledStartTime).Take(10))
                        {
                            <div class="d-flex align-items-center justify-content-between p-3 border rounded mb-3">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-2">
                                        <h6 class="mb-0 me-2">@meeting.Title</h6>
                                        <span class="badge bg-secondary">@meeting.Status</span>
                                    </div>
                                    <div class="text-muted small">
                                        <i class="fas fa-calendar me-1"></i>@meeting.ScheduledStartTime.ToString("MMM dd, yyyy")
                                        <span class="mx-2">•</span>
                                        <i class="fas fa-clock me-1"></i>@meeting.ScheduledStartTime.ToString("h:mm tt")
                                        @if (meeting.ActualEndTime.HasValue && meeting.ActualStartTime.HasValue)
                                        {
                                            var duration = meeting.ActualEndTime.Value - meeting.ActualStartTime.Value;
                                            var durationMinutes = (int)duration.TotalMinutes;
                                            <span class="mx-2">•</span>
                                            <i class="fas fa-stopwatch me-1"></i><text>@durationMinutes minutes</text>
                                        }
                                    </div>
                                </div>
                                <div class="flex-shrink-0">
                                    <a asp-action="Details" asp-route-id="@meeting.Id" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-eye me-1"></i>View
                                    </a>
                                    @if (!string.IsNullOrEmpty(meeting.RecordingUrl))
                                    {
                                        <a href="@meeting.RecordingUrl" target="_blank" class="btn btn-outline-primary btn-sm ms-1">
                                            <i class="fas fa-play me-1"></i>Recording
                                        </a>
                                    }
                                </div>
                            </div>
                        }
                        
                        @if (pastMeetings.Count() > 10)
                        {
                            <div class="text-center">
                                <button class="btn btn-outline-secondary" onclick="loadMoreMeetings()">
                                    <i class="fas fa-chevron-down me-2"></i>Load More
                                </button>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <p class="text-muted mb-0">No past meetings found</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function loadMoreMeetings() {
            // TODO: Implement pagination for past meetings
            showToast('Loading more meetings...', 'info');
        }
    </script>
}
