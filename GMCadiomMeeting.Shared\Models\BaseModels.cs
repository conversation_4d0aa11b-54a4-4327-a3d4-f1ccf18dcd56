using System.ComponentModel.DataAnnotations;

namespace GMCadiomMeeting.Shared.Models;

/// <summary>
/// Base class for all entities with common audit fields
/// </summary>
public abstract class BaseEntity
{
    /// <summary>
    /// Unique identifier for the entity
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Date and time when the entity was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Date and time when the entity was last updated
    /// </summary>
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// Indicates if the entity is soft deleted
    /// </summary>
    public bool IsDeleted { get; set; }

    /// <summary>
    /// Date and time when the entity was deleted (soft delete)
    /// </summary>
    public DateTime? DeletedAt { get; set; }
}

/// <summary>
/// Base class for API responses
/// </summary>
/// <typeparam name="T">Type of the response data</typeparam>
public class ApiResponse<T>
{
    /// <summary>
    /// Indicates if the request was successful
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Response data
    /// </summary>
    public T? Data { get; set; }

    /// <summary>
    /// Error message if the request failed
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// List of validation errors
    /// </summary>
    public List<string>? Errors { get; set; }

    /// <summary>
    /// HTTP status code
    /// </summary>
    public int StatusCode { get; set; }

    /// <summary>
    /// Timestamp of the response
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Creates a successful response
    /// </summary>
    /// <param name="data">Response data</param>
    /// <param name="message">Optional success message</param>
    /// <returns>Successful API response</returns>
    public static ApiResponse<T> Success(T data, string? message = null)
    {
        return new ApiResponse<T>
        {
            IsSuccess = true,
            Data = data,
            Message = message,
            StatusCode = 200
        };
    }

    /// <summary>
    /// Creates an error response
    /// </summary>
    /// <param name="message">Error message</param>
    /// <param name="statusCode">HTTP status code</param>
    /// <param name="errors">List of validation errors</param>
    /// <returns>Error API response</returns>
    public static ApiResponse<T> Error(string message, int statusCode = 400, List<string>? errors = null)
    {
        return new ApiResponse<T>
        {
            IsSuccess = false,
            Message = message,
            StatusCode = statusCode,
            Errors = errors
        };
    }
}

/// <summary>
/// Base class for paginated responses
/// </summary>
/// <typeparam name="T">Type of the items in the page</typeparam>
public class PagedResponse<T> : ApiResponse<List<T>>
{
    /// <summary>
    /// Current page number (1-based)
    /// </summary>
    public int PageNumber { get; set; }

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total number of items across all pages
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Total number of pages
    /// </summary>
    public int TotalPages { get; set; }

    /// <summary>
    /// Indicates if there is a previous page
    /// </summary>
    public bool HasPreviousPage => PageNumber > 1;

    /// <summary>
    /// Indicates if there is a next page
    /// </summary>
    public bool HasNextPage => PageNumber < TotalPages;

    /// <summary>
    /// Creates a successful paginated response
    /// </summary>
    /// <param name="data">List of items</param>
    /// <param name="pageNumber">Current page number</param>
    /// <param name="pageSize">Items per page</param>
    /// <param name="totalCount">Total number of items</param>
    /// <returns>Successful paginated response</returns>
    public static PagedResponse<T> Create(List<T> data, int pageNumber, int pageSize, int totalCount)
    {
        return new PagedResponse<T>
        {
            IsSuccess = true,
            Data = data,
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalCount = totalCount,
            TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
            StatusCode = 200
        };
    }
}

/// <summary>
/// Base class for requests with pagination
/// </summary>
public class PagedRequest
{
    /// <summary>
    /// Page number (1-based)
    /// </summary>
    [Range(1, int.MaxValue, ErrorMessage = "Page number must be greater than 0")]
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Number of items per page
    /// </summary>
    [Range(1, 100, ErrorMessage = "Page size must be between 1 and 100")]
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Search term for filtering
    /// </summary>
    [StringLength(100, ErrorMessage = "Search term cannot exceed 100 characters")]
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Field to sort by
    /// </summary>
    [StringLength(50, ErrorMessage = "Sort field cannot exceed 50 characters")]
    public string? SortBy { get; set; }

    /// <summary>
    /// Sort direction (asc/desc)
    /// </summary>
    [RegularExpression("^(asc|desc)$", ErrorMessage = "Sort direction must be 'asc' or 'desc'")]
    public string SortDirection { get; set; } = "asc";
}

/// <summary>
/// Base class for audit information
/// </summary>
public class AuditInfo
{
    /// <summary>
    /// User who created the entity
    /// </summary>
    public string? CreatedBy { get; set; }

    /// <summary>
    /// User who last updated the entity
    /// </summary>
    public string? UpdatedBy { get; set; }

    /// <summary>
    /// IP address of the user who created the entity
    /// </summary>
    public string? CreatedFromIp { get; set; }

    /// <summary>
    /// IP address of the user who last updated the entity
    /// </summary>
    public string? UpdatedFromIp { get; set; }

    /// <summary>
    /// User agent of the client that created the entity
    /// </summary>
    public string? CreatedFromUserAgent { get; set; }

    /// <summary>
    /// User agent of the client that last updated the entity
    /// </summary>
    public string? UpdatedFromUserAgent { get; set; }
}

/// <summary>
/// Base class for entities with full audit trail
/// </summary>
public abstract class AuditableEntity : BaseEntity
{
    /// <summary>
    /// Audit information for the entity
    /// </summary>
    public AuditInfo AuditInfo { get; set; } = new();
}

/// <summary>
/// Generic result class for operations
/// </summary>
/// <typeparam name="T">Type of the result data</typeparam>
public class Result<T>
{
    /// <summary>
    /// Indicates if the operation was successful
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Result data
    /// </summary>
    public T? Data { get; set; }

    /// <summary>
    /// Error message if the operation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// List of validation errors
    /// </summary>
    public List<string>? ValidationErrors { get; set; }

    /// <summary>
    /// Creates a successful result
    /// </summary>
    /// <param name="data">Result data</param>
    /// <returns>Successful result</returns>
    public static Result<T> Success(T data)
    {
        return new Result<T>
        {
            IsSuccess = true,
            Data = data
        };
    }

    /// <summary>
    /// Creates a failure result
    /// </summary>
    /// <param name="errorMessage">Error message</param>
    /// <param name="validationErrors">List of validation errors</param>
    /// <returns>Failure result</returns>
    public static Result<T> Failure(string errorMessage, List<string>? validationErrors = null)
    {
        return new Result<T>
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            ValidationErrors = validationErrors
        };
    }
}
