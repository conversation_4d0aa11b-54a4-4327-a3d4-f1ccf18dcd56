using GMCadiomMeeting.Shared.ViewModels.Meeting;
using GMCadiomMeeting.Shared.ViewModels.Participant;
using GMCadiomMeeting.Shared.ViewModels.User;

namespace GMCadiomMeeting.Web.Models;

/// <summary>
/// Web-specific ViewModels that extend or adapt the shared models for MVC views
/// These models are specifically designed for web forms and UI binding
/// </summary>

#region Dashboard ViewModels

/// <summary>
/// ViewModel for the user dashboard
/// </summary>
public class DashboardViewModel
{
    public UserDto User { get; set; } = new();
    public List<MeetingDto> UpcomingMeetings { get; set; } = new();
    public List<MeetingDto> ActiveMeetings { get; set; } = new();
    public List<InvitationDto> PendingInvitations { get; set; } = new();
    public DashboardStats Stats { get; set; } = new();
}

/// <summary>
/// Dashboard statistics
/// </summary>
public class DashboardStats
{
    public int TotalMeetings { get; set; }
    public int UpcomingMeetings { get; set; }
    public int ActiveMeetings { get; set; }
    public int CompletedMeetings { get; set; }
    public int PendingInvitations { get; set; }
    public int TotalParticipants { get; set; }
    public int TotalMinutesInMeetings { get; set; }
}

#endregion

#region Utility ViewModels



/// <summary>
/// ViewModel for pagination
/// </summary>
public class PaginationViewModel
{
    public int CurrentPage { get; set; }
    public int TotalPages { get; set; }
    public int PageSize { get; set; }
    public int TotalItems { get; set; }
    public bool HasPreviousPage => CurrentPage > 1;
    public bool HasNextPage => CurrentPage < TotalPages;
    public string? SearchTerm { get; set; }
    public string? SortBy { get; set; }
    public string? SortDirection { get; set; }
}

/// <summary>
/// ViewModel for select list items
/// </summary>
public class SelectListItemViewModel
{
    public string Value { get; set; } = string.Empty;
    public string Text { get; set; } = string.Empty;
    public bool Selected { get; set; }
    public bool Disabled { get; set; }
}

#endregion
