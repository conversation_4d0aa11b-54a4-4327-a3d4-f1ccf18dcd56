﻿using System.ComponentModel.DataAnnotations;

namespace GMCadiomMeeting.Shared.ViewModels.User.UpdateUser
{
    /// <summary>
    /// Request model for updating user profile
    /// </summary>
    public class UpdateUserRequest
    {
        /// <summary>
        /// User's display name
        /// </summary>
        [Required(ErrorMessage = "Display name is required")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "Display name must be between 2 and 100 characters")]
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// User's first name
        /// </summary>
        [StringLength(50, ErrorMessage = "First name cannot exceed 50 characters")]
        public string? FirstName { get; set; }

        /// <summary>
        /// User's last name
        /// </summary>
        [StringLength(50, ErrorMessage = "Last name cannot exceed 50 characters")]
        public string? LastName { get; set; }

        /// <summary>
        /// URL to user's profile picture
        /// </summary>
        [Url(ErrorMessage = "Invalid URL format")]
        [StringLength(500, ErrorMessage = "Profile picture URL cannot exceed 500 characters")]
        public string? ProfilePictureUrl { get; set; }

        /// <summary>
        /// User's timezone
        /// </summary>
        [StringLength(50, ErrorMessage = "Timezone cannot exceed 50 characters")]
        public string? TimeZone { get; set; }
    }
}
