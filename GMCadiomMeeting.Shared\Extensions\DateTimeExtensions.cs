namespace GMCadiomMeeting.Shared.Extensions;

/// <summary>
/// Extension methods for DateTime operations
/// </summary>
public static class DateTimeExtensions
{
    /// <summary>
    /// Converts UTC DateTime to specified timezone
    /// </summary>
    /// <param name="utcDateTime">UTC DateTime</param>
    /// <param name="timeZoneId">Target timezone ID</param>
    /// <returns>DateTime in target timezone</returns>
    public static DateTime ToTimeZone(this DateTime utcDateTime, string timeZoneId)
    {
        if (string.IsNullOrWhiteSpace(timeZoneId))
            return utcDateTime;

        try
        {
            var timeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
            return TimeZoneInfo.ConvertTimeFromUtc(utcDateTime, timeZone);
        }
        catch
        {
            return utcDateTime;
        }
    }

    /// <summary>
    /// Converts DateTime from specified timezone to UTC
    /// </summary>
    /// <param name="dateTime">DateTime in source timezone</param>
    /// <param name="timeZoneId">Source timezone ID</param>
    /// <returns>UTC DateTime</returns>
    public static DateTime FromTimeZone(this DateTime dateTime, string timeZoneId)
    {
        if (string.IsNullOrWhiteSpace(timeZoneId))
            return dateTime;

        try
        {
            var timeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
            return TimeZoneInfo.ConvertTimeToUtc(dateTime, timeZone);
        }
        catch
        {
            return dateTime;
        }
    }

    /// <summary>
    /// Gets a human-readable relative time string
    /// </summary>
    /// <param name="dateTime">DateTime to format</param>
    /// <param name="relativeTo">Reference DateTime (default: now)</param>
    /// <returns>Relative time string</returns>
    public static string ToRelativeTime(this DateTime dateTime, DateTime? relativeTo = null)
    {
        var now = relativeTo ?? DateTime.UtcNow;
        var timeSpan = now - dateTime;

        if (timeSpan.TotalSeconds < 60)
            return "just now";

        if (timeSpan.TotalMinutes < 60)
            return $"{(int)timeSpan.TotalMinutes} minute{(timeSpan.TotalMinutes >= 2 ? "s" : "")} ago";

        if (timeSpan.TotalHours < 24)
            return $"{(int)timeSpan.TotalHours} hour{(timeSpan.TotalHours >= 2 ? "s" : "")} ago";

        if (timeSpan.TotalDays < 7)
            return $"{(int)timeSpan.TotalDays} day{(timeSpan.TotalDays >= 2 ? "s" : "")} ago";

        if (timeSpan.TotalDays < 30)
            return $"{(int)(timeSpan.TotalDays / 7)} week{(timeSpan.TotalDays >= 14 ? "s" : "")} ago";

        if (timeSpan.TotalDays < 365)
            return $"{(int)(timeSpan.TotalDays / 30)} month{(timeSpan.TotalDays >= 60 ? "s" : "")} ago";

        return $"{(int)(timeSpan.TotalDays / 365)} year{(timeSpan.TotalDays >= 730 ? "s" : "")} ago";
    }

    /// <summary>
    /// Gets time until a future DateTime
    /// </summary>
    /// <param name="futureDateTime">Future DateTime</param>
    /// <param name="relativeTo">Reference DateTime (default: now)</param>
    /// <returns>Time until string</returns>
    public static string ToTimeUntil(this DateTime futureDateTime, DateTime? relativeTo = null)
    {
        var now = relativeTo ?? DateTime.UtcNow;
        var timeSpan = futureDateTime - now;

        if (timeSpan.TotalSeconds <= 0)
            return "now";

        if (timeSpan.TotalMinutes < 60)
            return $"in {(int)timeSpan.TotalMinutes} minute{(timeSpan.TotalMinutes >= 2 ? "s" : "")}";

        if (timeSpan.TotalHours < 24)
            return $"in {(int)timeSpan.TotalHours} hour{(timeSpan.TotalHours >= 2 ? "s" : "")}";

        if (timeSpan.TotalDays < 7)
            return $"in {(int)timeSpan.TotalDays} day{(timeSpan.TotalDays >= 2 ? "s" : "")}";

        return futureDateTime.ToString("MMM dd, yyyy");
    }

    /// <summary>
    /// Checks if DateTime is within a specified range
    /// </summary>
    /// <param name="dateTime">DateTime to check</param>
    /// <param name="start">Range start</param>
    /// <param name="end">Range end</param>
    /// <returns>True if within range</returns>
    public static bool IsWithinRange(this DateTime dateTime, DateTime start, DateTime end)
    {
        return dateTime >= start && dateTime <= end;
    }

    /// <summary>
    /// Gets the start of the day (00:00:00)
    /// </summary>
    /// <param name="dateTime">DateTime</param>
    /// <returns>Start of day</returns>
    public static DateTime StartOfDay(this DateTime dateTime)
    {
        return dateTime.Date;
    }

    /// <summary>
    /// Gets the end of the day (23:59:59.999)
    /// </summary>
    /// <param name="dateTime">DateTime</param>
    /// <returns>End of day</returns>
    public static DateTime EndOfDay(this DateTime dateTime)
    {
        return dateTime.Date.AddDays(1).AddTicks(-1);
    }

    /// <summary>
    /// Gets the start of the week (Monday)
    /// </summary>
    /// <param name="dateTime">DateTime</param>
    /// <returns>Start of week</returns>
    public static DateTime StartOfWeek(this DateTime dateTime)
    {
        var diff = (7 + (dateTime.DayOfWeek - DayOfWeek.Monday)) % 7;
        return dateTime.AddDays(-1 * diff).Date;
    }

    /// <summary>
    /// Gets the start of the month
    /// </summary>
    /// <param name="dateTime">DateTime</param>
    /// <returns>Start of month</returns>
    public static DateTime StartOfMonth(this DateTime dateTime)
    {
        return new DateTime(dateTime.Year, dateTime.Month, 1);
    }

    /// <summary>
    /// Formats DateTime for meeting display
    /// </summary>
    /// <param name="dateTime">DateTime to format</param>
    /// <param name="timeZoneId">Target timezone</param>
    /// <returns>Formatted string</returns>
    public static string ToMeetingFormat(this DateTime dateTime, string? timeZoneId = null)
    {
        var localDateTime = string.IsNullOrWhiteSpace(timeZoneId)
            ? dateTime
            : dateTime.ToTimeZone(timeZoneId);

        return localDateTime.ToString("MMM dd, yyyy h:mm tt");
    }

    /// <summary>
    /// Formats DateTime for short display
    /// </summary>
    /// <param name="dateTime">DateTime to format</param>
    /// <returns>Short formatted string</returns>
    public static string ToShortFormat(this DateTime dateTime)
    {
        return dateTime.ToString("MMM dd, h:mm tt");
    }

    /// <summary>
    /// Checks if DateTime is today
    /// </summary>
    /// <param name="dateTime">DateTime to check</param>
    /// <param name="timeZoneId">Timezone for comparison</param>
    /// <returns>True if today</returns>
    public static bool IsToday(this DateTime dateTime, string? timeZoneId = null)
    {
        var now = DateTime.UtcNow;
        var localDateTime = string.IsNullOrWhiteSpace(timeZoneId)
            ? dateTime
            : dateTime.ToTimeZone(timeZoneId);
        var localNow = string.IsNullOrWhiteSpace(timeZoneId)
            ? now
            : now.ToTimeZone(timeZoneId);

        return localDateTime.Date == localNow.Date;
    }

    /// <summary>
    /// Checks if DateTime is tomorrow
    /// </summary>
    /// <param name="dateTime">DateTime to check</param>
    /// <param name="timeZoneId">Timezone for comparison</param>
    /// <returns>True if tomorrow</returns>
    public static bool IsTomorrow(this DateTime dateTime, string? timeZoneId = null)
    {
        var now = DateTime.UtcNow;
        var localDateTime = string.IsNullOrWhiteSpace(timeZoneId)
            ? dateTime
            : dateTime.ToTimeZone(timeZoneId);
        var localNow = string.IsNullOrWhiteSpace(timeZoneId)
            ? now
            : now.ToTimeZone(timeZoneId);

        return localDateTime.Date == localNow.Date.AddDays(1);
    }

    /// <summary>
    /// Rounds DateTime to nearest interval
    /// </summary>
    /// <param name="dateTime">DateTime to round</param>
    /// <param name="interval">Interval in minutes</param>
    /// <returns>Rounded DateTime</returns>
    public static DateTime RoundToNearestInterval(this DateTime dateTime, int interval)
    {
        var totalMinutes = (int)(dateTime.TimeOfDay.TotalMinutes);
        var roundedMinutes = ((totalMinutes + interval / 2) / interval) * interval;

        return dateTime.Date.AddMinutes(roundedMinutes);
    }

    /// <summary>
    /// Gets duration between two DateTimes in a readable format
    /// </summary>
    /// <param name="startTime">Start DateTime</param>
    /// <param name="endTime">End DateTime</param>
    /// <returns>Duration string</returns>
    public static string GetDurationString(this DateTime startTime, DateTime endTime)
    {
        var duration = endTime - startTime;

        if (duration.TotalMinutes < 1)
            return "Less than a minute";

        if (duration.TotalHours < 1)
            return $"{(int)duration.TotalMinutes} minute{(duration.TotalMinutes >= 2 ? "s" : "")}";

        var hours = (int)duration.TotalHours;
        var minutes = duration.Minutes;

        if (minutes == 0)
            return $"{hours} hour{(hours >= 2 ? "s" : "")}";

        return $"{hours} hour{(hours >= 2 ? "s" : "")} {minutes} minute{(minutes >= 2 ? "s" : "")}";
    }
}
