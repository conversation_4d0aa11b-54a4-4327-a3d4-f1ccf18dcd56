# GMCadiom Meeting - Video Conferencing Application

[![.NET](https://img.shields.io/badge/.NET-9.0-blue.svg)](https://dotnet.microsoft.com/)
[![MySQL](https://img.shields.io/badge/MySQL-8.0+-orange.svg)](https://www.mysql.com/)
[![Entity Framework](https://img.shields.io/badge/EF%20Core-9.0-green.svg)](https://docs.microsoft.com/en-us/ef/)
[![SignalR](https://img.shields.io/badge/SignalR-Real--time-red.svg)](https://docs.microsoft.com/en-us/aspnet/core/signalr/)

A comprehensive video conferencing application built with C# .NET Core 9, MySQL, and Entity Framework Core 9. Provides complete functionality for one-on-one and group video calling, screen sharing, and meeting management similar to Google Meet or Zoom.

## 🚀 Features

### Core Functionality
- **Video Conferencing**: One-on-one and group video calls with WebRTC
- **Screen Sharing**: Share full screen, application windows, or browser tabs
- **Meeting Management**: Create, schedule, join, and manage meetings
- **Real-time Chat**: In-meeting messaging with file attachments
- **User Management**: Registration, authentication, and profile management
- **Invitation System**: Send and manage meeting invitations

### Technical Features
- **Real-time Communication**: SignalR WebSocket connections
- **RESTful API**: Comprehensive REST API with 20+ endpoints
- **Database Design**: Optimized MySQL schema with 6 core entities
- **Business Logic**: Service layer with validation and authorization
- **Security**: Password hashing, role-based access control
- **Performance**: Strategic database indexing and query optimization

## 🏗️ Architecture

### Technology Stack
- **Backend**: C# .NET Core 9
- **Database**: MySQL 8.0+ with Pomelo provider
- **ORM**: Entity Framework Core 9
- **Real-time**: SignalR for WebSocket communication
- **API**: RESTful API with OpenAPI documentation

### Project Structure
```
GMCadiomMeeting/
├── GMCadiomMeeting.Api/          # Web API project
│   ├── Controllers/              # API controllers
│   ├── Services/                 # Business logic services
│   ├── Hubs/                     # SignalR hubs
│   └── Program.cs               # Application configuration
├── GMCadiomMeeting.Data/         # Data layer
│   ├── Models/                   # Entity models
│   ├── Context/                  # DbContext and factory
│   ├── Configurations/           # Entity configurations
│   └── Migrations/               # EF Core migrations
└── Documentation/                # Comprehensive documentation
```

## 🚀 Quick Start

### Prerequisites
- .NET 9 SDK
- MySQL 8.0+ Server
- Visual Studio 2022 or VS Code

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd GMCadiomMeeting
```

2. **Setup MySQL Database**
```sql
CREATE DATABASE GMCadiomMeeting;
CREATE USER 'gmcadiom'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON GMCadiomMeeting.* TO 'gmcadiom'@'localhost';
FLUSH PRIVILEGES;
```

3. **Configure Connection String**
Update `GMCadiomMeeting.Api/appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=GMCadiomMeeting;User=gmcadiom;Password=your_password;"
  }
}
```

4. **Apply Database Migrations**
```bash
cd GMCadiomMeeting.Api
dotnet ef database update --project ../GMCadiomMeeting.Data
```

5. **Run the Application**
```bash
dotnet run
```

The API will be available at `https://localhost:7000`

## 📚 Documentation

### Comprehensive Guides
- **[Database Schema Documentation](DATABASE_SCHEMA_DOCUMENTATION.md)** - Complete database design explanation
- **[Quick Setup Guide](QUICK_SETUP_GUIDE.md)** - Step-by-step setup instructions
- **[API Documentation](API_DOCUMENTATION.md)** - Complete API reference
- **[Deployment Guide](DEPLOYMENT_GUIDE.md)** - Production deployment instructions
- **[Project Summary](PROJECT_SUMMARY.md)** - Comprehensive project overview

## 🔌 API Endpoints

### Users
- `POST /api/users/register` - User registration
- `POST /api/users/login` - User authentication
- `GET /api/users/{id}` - Get user details
- `PUT /api/users/{id}` - Update user profile
- `GET /api/users/search` - Search users

### Meetings
- `POST /api/meetings` - Create meeting
- `GET /api/meetings/user/{userId}` - Get user meetings
- `GET /api/meetings/{id}` - Get meeting details
- `POST /api/meetings/{code}/join` - Join meeting

### Invitations
- `POST /api/invitations/send` - Send invitations
- `GET /api/invitations/meeting/{id}` - Get meeting invitations
- `POST /api/invitations/{id}/respond` - Respond to invitation

### Chat
- `POST /api/chat/send` - Send message
- `GET /api/chat/meeting/{id}` - Get meeting messages
- `PUT /api/chat/{id}` - Edit message

### SignalR Hub (`/meetingHub`)
- `JoinMeeting` - Join meeting room
- `SendChatMessage` - Real-time messaging
- `UpdateMediaState` - Camera/microphone updates
- `StartScreenSharing` - Begin screen sharing
- `SendSignal` - WebRTC signaling

## 🗄️ Database Schema

### Core Entities
1. **Users** - User accounts and authentication
2. **Meetings** - Meeting definitions and scheduling
3. **MeetingParticipants** - User participation in meetings
4. **Invitations** - Meeting invitations
5. **ChatMessages** - In-meeting chat
6. **ScreenSharingMetadata** - Screen sharing sessions

### Key Relationships
- Users can host multiple meetings
- Users can participate in multiple meetings
- Meetings can have multiple participants and invitations
- Real-time chat with public and private messaging
- Screen sharing with metadata tracking

## 🔧 Development

### Building the Project
```bash
# Restore packages
dotnet restore

# Build solution
dotnet build

# Run tests (when available)
dotnet test

# Run application
dotnet run --project GMCadiomMeeting.Api
```

### Database Operations
```bash
# Add new migration
dotnet ef migrations add MigrationName --project GMCadiomMeeting.Data

# Update database
dotnet ef database update --project GMCadiomMeeting.Data

# Generate SQL script
dotnet ef migrations script --project GMCadiomMeeting.Data
```

## 🌐 Real-time Communication

### SignalR JavaScript Client Example
```javascript
const connection = new signalR.HubConnectionBuilder()
    .withUrl("/meetingHub")
    .build();

// Start connection
await connection.start();

// Join meeting
await connection.invoke("JoinMeeting", meetingId, userId, displayName);

// Listen for events
connection.on("ParticipantJoined", (participant) => {
    console.log("Participant joined:", participant);
});

connection.on("ChatMessage", (message) => {
    console.log("New message:", message);
});

// Send chat message
await connection.invoke("SendChatMessage", meetingId, "Hello!", "text");
```

## 🔒 Security Features

- **Password Security**: Secure hashing with salt
- **Role-based Access**: Granular permissions for meeting participants
- **Input Validation**: Comprehensive server-side validation
- **SQL Injection Protection**: Entity Framework parameterized queries
- **HTTPS Enforcement**: SSL/TLS configuration for production

## 📈 Performance Features

- **Database Indexing**: Strategic indexes for common queries
- **Connection Pooling**: Optimized database connections
- **Query Optimization**: Efficient Entity Framework queries
- **Real-time Scaling**: SignalR with Redis backplane support
- **Response Compression**: Optimized API responses

## 🚀 Production Deployment

### Docker Support
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:9.0
WORKDIR /app
COPY publish/ .
ENTRYPOINT ["dotnet", "GMCadiomMeeting.Api.dll"]
```

### Environment Configuration
- Production connection strings
- SSL certificate configuration
- Logging and monitoring setup
- Health check endpoints
- Performance optimization

See [Deployment Guide](DEPLOYMENT_GUIDE.md) for detailed instructions.

## 🧪 Testing

### API Testing with curl
```bash
# Register user
curl -X POST https://localhost:7000/api/users/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Test123","displayName":"Test User"}'

# Create meeting
curl -X POST https://localhost:7000/api/meetings \
  -H "Content-Type: application/json" \
  -d '{"title":"Test Meeting","hostUserId":1,"scheduledStartTime":"2025-01-07T10:00:00Z","scheduledEndTime":"2025-01-07T11:00:00Z"}'
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For questions or issues:
1. Check the comprehensive documentation
2. Review the API documentation
3. Check the deployment guide
4. Create an issue in the repository

## 🎯 Future Enhancements

- JWT authentication implementation
- File upload and sharing in chat
- Meeting recording and playback
- Breakout rooms functionality
- Mobile app support
- Advanced analytics and reporting

---

**GMCadiom Meeting** - A production-ready video conferencing solution built with modern .NET technologies.
