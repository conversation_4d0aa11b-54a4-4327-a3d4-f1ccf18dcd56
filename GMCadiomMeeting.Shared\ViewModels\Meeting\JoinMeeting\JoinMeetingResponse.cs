using GMCadiomMeeting.Shared.ViewModels.Participant;

namespace GMCadiomMeeting.Shared.ViewModels.Meeting.JoinMeeting;

/// <summary>
/// Response model for joining a meeting
/// </summary>
public class JoinMeetingResponse
{
    /// <summary>
    /// Meeting information
    /// </summary>
    public MeetingDto Meeting { get; set; } = new();

    /// <summary>
    /// Participant information
    /// </summary>
    public MeetingParticipantDto Participant { get; set; } = new();

    /// <summary>
    /// SignalR connection token
    /// </summary>
    public string? ConnectionToken { get; set; }
}
