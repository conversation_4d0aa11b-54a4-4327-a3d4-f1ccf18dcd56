using GMCadiomMeeting.Services.UserServices;
using GMCadiomMeeting.Shared.ViewModels.User;
using GMCadiomMeeting.Shared.ViewModels.User.Login;
using GMCadiomMeeting.Shared.ViewModels.User.RegisterUser;
using GMCadiomMeeting.Shared.ViewModels.User.UpdateUser;
using Microsoft.AspNetCore.Mvc;

namespace GMCadiomMeeting.Api.Controllers;

/// <summary>
/// Controller for user management operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class UsersController(UserService userService, ILogger<UsersController> logger) : ControllerBase
{
    /// <summary>
    /// Register a new user
    /// </summary>
    [HttpPost("register")]
    public async Task<ActionResult<UserDto>> Register(RegisterUserRequest request)
    {
        try
        {
            var response = await userService.Register(request);
            return CreatedAtAction(nameof(GetUser), new { id = response.Id }, response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error registering user with email {Email}", request.Email);
            return StatusCode(500, "An error occurred while registering the user");
        }
    }

    /// <summary>
    /// Authenticate user login
    /// </summary>
    [HttpPost("login")]
    public async Task<ActionResult<LoginResponse>> Login(LoginRequest request)
    {
        try
        {
            var response = await userService.Login(request);
            return Ok(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during login for email {Email}", request.Email);
            return StatusCode(500, "An error occurred during login");
        }
    }

    /// <summary>
    /// Get user by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<UserDto>> GetUser(int id)
    {
        try
        {
            var response = await userService.GetUser(id);
            return Ok(response);
        }
        catch (KeyNotFoundException ex)
        {
            logger.LogWarning(ex, "User not found {UserId}", id);
            return NotFound($"User with ID {id} not found");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving user {UserId}", id);
            return StatusCode(500, "An error occurred while retrieving the user");
        }
    }

    /// <summary>
    /// Update user profile
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<UserDto>> UpdateUser(int id, UpdateUserRequest request)
    {
        try
        {
            var response = await userService.UpdateUser(id, request);
            return Ok(response);
        }
        catch (KeyNotFoundException ex)
        {
            logger.LogWarning(ex, "User not found for update {UserId}", id);
            return NotFound($"User with ID {id} not found");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating user {UserId}", id);
            return StatusCode(500, "An error occurred while updating the user");
        }
    }

    /// <summary>
    /// Search users by email or name
    /// </summary>
    [HttpGet("search")]
    public async Task<ActionResult<IEnumerable<UserDto>>> SearchUsers([FromQuery] string query, [FromQuery] int limit = 10)
    {
        try
        {
            var response = await userService.SearchUsers(query, limit);
            return Ok(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error searching users with query {Query}", query);
            return StatusCode(500, "An error occurred while searching users");
        }
    }
}
