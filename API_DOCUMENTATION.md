# GMCadiom Meeting API Documentation

## Overview

This document provides comprehensive API documentation for the GMCadiom Meeting video conferencing application. The API supports user management, meeting creation and management, invitations, chat functionality, and real-time communication via SignalR.

## Base URL

```
Development: https://localhost:7000
Production: https://api.gmcadiom.com
```

## Authentication

Currently using basic authentication. JWT implementation is planned for production.

## API Endpoints

### Users Controller

#### POST /api/users/register
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123",
  "displayName": "<PERSON>",
  "firstName": "<PERSON>",
  "lastName": "Doe",
  "timeZone": "America/New_York"
}
```

**Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "displayName": "<PERSON>",
  "firstName": "John",
  "lastName": "Doe",
  "isActive": true,
  "isEmailVerified": false,
  "createdAt": "2025-01-06T20:00:00Z"
}
```

#### POST /api/users/login
Authenticate user login.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123"
}
```

**Response:**
```json
{
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "displayName": "John Doe"
  },
  "token": "jwt_token_placeholder",
  "expiresAt": "2025-01-07T20:00:00Z"
}
```

#### GET /api/users/{id}
Get user details by ID.

#### PUT /api/users/{id}
Update user profile.

#### GET /api/users/search?query={searchTerm}&limit={limit}
Search users by email or name.

### Meetings Controller

#### POST /api/meetings
Create a new meeting.

**Request Body:**
```json
{
  "title": "Team Standup",
  "description": "Daily team standup meeting",
  "type": 1,
  "scheduledStartTime": "2025-01-07T09:00:00Z",
  "scheduledEndTime": "2025-01-07T10:00:00Z",
  "hostUserId": 1,
  "maxParticipants": 10,
  "isRecordingEnabled": false
}
```

#### GET /api/meetings/user/{userId}
Get all meetings for a specific user.

#### GET /api/meetings/{id}
Get meeting details by ID.

#### POST /api/meetings/{meetingCode}/join
Join a meeting using meeting code.

**Request Body:**
```json
{
  "userId": 2,
  "displayName": "Jane Smith",
  "isCameraEnabled": true,
  "isMicrophoneEnabled": false
}
```

### Invitations Controller

#### POST /api/invitations/send
Send meeting invitations.

**Request Body:**
```json
{
  "meetingId": 1,
  "sentByUserId": 1,
  "invitees": [
    {
      "email": "<EMAIL>",
      "name": "Colleague Name",
      "role": 3
    }
  ],
  "personalMessage": "Please join our team meeting",
  "expiresAt": "2025-01-07T08:00:00Z",
  "allowJoinBeforeHost": false
}
```

#### GET /api/invitations/meeting/{meetingId}
Get invitations for a meeting.

#### GET /api/invitations/user/{userId}
Get invitations for a user.

#### POST /api/invitations/{invitationId}/respond
Respond to an invitation (accept/decline).

**Request Body:**
```json
{
  "accept": true
}
```

#### POST /api/invitations/join/{token}
Join meeting via invitation token.

### Chat Controller

#### POST /api/chat/send
Send a chat message in a meeting.

**Request Body:**
```json
{
  "meetingId": 1,
  "senderId": 2,
  "content": "Hello everyone!",
  "type": 0,
  "scope": 0
}
```

#### GET /api/chat/meeting/{meetingId}?userId={userId}&page={page}&pageSize={pageSize}
Get chat messages for a meeting.

#### PUT /api/chat/{id}
Edit a chat message.

#### DELETE /api/chat/{id}?userId={userId}
Delete a chat message.

#### GET /api/chat/meeting/{meetingId}/conversation?userId1={userId1}&userId2={userId2}
Get private conversation between two users.

## SignalR Hub

### Connection URL
```
/meetingHub
```

### Hub Methods

#### JoinMeeting(meetingId, userId, displayName)
Join a meeting room for real-time communication.

#### LeaveMeeting()
Leave the current meeting room.

#### SendChatMessage(meetingId, message, messageType, recipientId)
Send a real-time chat message.

#### UpdateMediaState(meetingId, isCameraEnabled, isMicrophoneEnabled)
Update participant's camera and microphone state.

#### StartScreenSharing(meetingId, sessionId, shareType)
Start screen sharing session.

#### StopScreenSharing(meetingId, sessionId)
Stop screen sharing session.

#### SendSignal(meetingId, targetUserId, signal, signalType)
Send WebRTC signaling data for video calls.

### Hub Events (Client-side)

#### ParticipantJoined
Fired when a participant joins the meeting.

#### ParticipantLeft
Fired when a participant leaves the meeting.

#### ChatMessage
Fired when a chat message is received.

#### ParticipantMediaStateChanged
Fired when a participant's media state changes.

#### ScreenSharingStarted
Fired when screen sharing starts.

#### ScreenSharingStopped
Fired when screen sharing stops.

#### ReceiveSignal
Fired when WebRTC signaling data is received.

## Data Models

### Enums

#### MeetingType
- OneOnOne = 0
- Group = 1
- Webinar = 2

#### MeetingStatus
- Scheduled = 0
- InProgress = 1
- Ended = 2
- Cancelled = 3

#### ParticipantRole
- Host = 0
- CoHost = 1
- Presenter = 2
- Attendee = 3
- Observer = 4

#### ConnectionStatus
- Connected = 0
- Disconnected = 1
- Reconnecting = 2
- Left = 3

#### MessageType
- Text = 0
- File = 1
- Image = 2
- System = 3
- Emoji = 4
- Link = 5

#### MessageScope
- Public = 0
- Private = 1
- Hosts = 2
- Presenters = 3

## Error Handling

All API endpoints return appropriate HTTP status codes:

- **200 OK**: Successful request
- **201 Created**: Resource created successfully
- **400 Bad Request**: Invalid request data
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **500 Internal Server Error**: Server error

Error responses include a message:
```json
{
  "error": "Error description"
}
```

## Rate Limiting

Currently no rate limiting is implemented. Consider implementing rate limiting for production use.

## WebSocket Connection (SignalR)

### JavaScript Client Example

```javascript
const connection = new signalR.HubConnectionBuilder()
    .withUrl("/meetingHub")
    .build();

// Start connection
await connection.start();

// Join meeting
await connection.invoke("JoinMeeting", meetingId, userId, displayName);

// Listen for events
connection.on("ParticipantJoined", (participant) => {
    console.log("Participant joined:", participant);
});

connection.on("ChatMessage", (message) => {
    console.log("New message:", message);
});

// Send chat message
await connection.invoke("SendChatMessage", meetingId, "Hello!", "text");
```

## Security Considerations

1. **Input Validation**: All inputs are validated on the server side
2. **SQL Injection Protection**: Using Entity Framework with parameterized queries
3. **XSS Protection**: Content is properly escaped
4. **CORS**: Configured for development (should be restricted in production)
5. **Authentication**: JWT implementation recommended for production
6. **Authorization**: Role-based access control implemented

## Performance Considerations

1. **Database Indexing**: Strategic indexes for common queries
2. **Connection Pooling**: Entity Framework connection pooling
3. **SignalR Scaling**: Consider Redis backplane for multiple servers
4. **Caching**: Consider implementing caching for frequently accessed data

## Deployment Notes

1. **Database**: Ensure MySQL server is properly configured
2. **Connection Strings**: Update for production environment
3. **HTTPS**: Enable HTTPS in production
4. **Logging**: Configure appropriate logging levels
5. **Monitoring**: Implement health checks and monitoring

## Testing

Use tools like Postman or curl to test API endpoints:

```bash
# Register user
curl -X POST https://localhost:7000/api/users/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Test123","displayName":"Test User"}'

# Create meeting
curl -X POST https://localhost:7000/api/meetings \
  -H "Content-Type: application/json" \
  -d '{"title":"Test Meeting","hostUserId":1,"scheduledStartTime":"2025-01-07T10:00:00Z","scheduledEndTime":"2025-01-07T11:00:00Z"}'
```

For SignalR testing, use the SignalR client libraries or browser developer tools to test WebSocket connections.
