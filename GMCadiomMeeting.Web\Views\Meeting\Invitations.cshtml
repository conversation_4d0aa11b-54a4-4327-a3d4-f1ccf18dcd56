@using GMCadiomMeeting.Shared.ViewModels.Meeting
@using GMCadiomMeeting.Shared.ViewModels.Participant
@model IEnumerable<InvitationDto>
@{
    ViewData["Title"] = "Meeting Invitations";
    var meeting = ViewBag.Meeting as MeetingDto;
    var pendingInvitations = Model.Where(i => i.Status == GMCadiomMeeting.Shared.Enums.InvitationStatus.Pending);
    var acceptedInvitations = Model.Where(i => i.Status == GMCadiomMeeting.Shared.Enums.InvitationStatus.Accepted);
    var declinedInvitations = Model.Where(i => i.Status == GMCadiomMeeting.Shared.Enums.InvitationStatus.Declined);
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">Meeting Invitations</h1>
                    <p class="text-muted mb-0">@meeting?.Title</p>
                </div>
                <div class="d-flex gap-2">
                    <a asp-action="SendInvitation" asp-route-id="@meeting?.Id" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-2"></i>Send Invitations
                    </a>
                    <a asp-action="Details" asp-route-id="@meeting?.Id" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Meeting
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Meeting Info -->
    @if (meeting != null)
    {
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h6 class="mb-2">@meeting.Title</h6>
                                <div class="text-muted small">
                                    <i class="fas fa-calendar me-1"></i>@meeting.ScheduledStartTime.ToString("MMM dd, yyyy h:mm tt")
                                    <span class="mx-2">•</span>
                                    <i class="fas fa-key me-1"></i>@meeting.MeetingCode
                                    <span class="mx-2">•</span>
                                    <span class="badge bg-@(meeting.Status == GMCadiomMeeting.Shared.Enums.MeetingStatus.Scheduled ? "warning" : 
                                                          meeting.Status == GMCadiomMeeting.Shared.Enums.MeetingStatus.InProgress ? "success" : "secondary")">
                                        @meeting.Status
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="btn-group">
                                    <button class="btn btn-sm btn-outline-primary" onclick="copyInvitationLink()">
                                        <i class="fas fa-link me-1"></i>Copy Link
                                    </button>
                                    <button class="btn btn-sm btn-outline-success" onclick="copyMeetingCode()">
                                        <i class="fas fa-copy me-1"></i>Copy Code
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Invitation Stats -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fas fa-clock"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold text-warning">@pendingInvitations.Count()</div>
                            <div class="text-muted small">Pending</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold text-success">@acceptedInvitations.Count()</div>
                            <div class="text-muted small">Accepted</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-danger text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fas fa-times"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold text-danger">@declinedInvitations.Count()</div>
                            <div class="text-muted small">Declined</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fas fa-envelope"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold text-info">@Model.Count()</div>
                            <div class="text-muted small">Total Sent</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Invitations List -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom py-3">
                    <h5 class="mb-0">
                        <i class="fas fa-envelope text-primary me-2"></i>All Invitations (@Model.Count())
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Recipient</th>
                                        <th>Role</th>
                                        <th>Status</th>
                                        <th>Sent</th>
                                        <th>Responded</th>
                                        <th>Expires</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var invitation in Model.OrderByDescending(i => i.SentAt))
                                    {
                                        <tr>
                                            <td>
                                                <div class="fw-semibold">@invitation.InviteeEmail</div>
                                                @if (!string.IsNullOrEmpty(invitation.PersonalMessage))
                                                {
                                                    <div class="small text-muted">@invitation.PersonalMessage</div>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge bg-@(invitation.InvitedRole == GMCadiomMeeting.Shared.Enums.ParticipantRole.Host ? "primary" : 
                                                                      invitation.InvitedRole == GMCadiomMeeting.Shared.Enums.ParticipantRole.Presenter ? "warning" : "secondary")">
                                                    @invitation.InvitedRole
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-@(invitation.Status == GMCadiomMeeting.Shared.Enums.InvitationStatus.Pending ? "warning" : 
                                                                      invitation.Status == GMCadiomMeeting.Shared.Enums.InvitationStatus.Accepted ? "success" : 
                                                                      invitation.Status == GMCadiomMeeting.Shared.Enums.InvitationStatus.Declined ? "danger" : "secondary")">
                                                    @invitation.Status
                                                </span>
                                            </td>
                                            <td>
                                                <div>@invitation.SentAt.ToString("MMM dd, yyyy")</div>
                                                <div class="small text-muted">@invitation.SentAt.ToString("h:mm tt")</div>
                                            </td>
                                            <td>
                                                @if (invitation.RespondedAt.HasValue)
                                                {
                                                    <div>@invitation.RespondedAt.Value.ToString("MMM dd, yyyy")</div>
                                                    <div class="small text-muted">@invitation.RespondedAt.Value.ToString("h:mm tt")</div>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                @if (invitation.ExpiresAt.HasValue)
                                                {
                                                    <div>@invitation.ExpiresAt.Value.ToString("MMM dd, yyyy")</div>
                                                    <div class="small text-muted">@invitation.ExpiresAt.Value.ToString("h:mm tt")</div>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">Never</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    @if (invitation.Status == GMCadiomMeeting.Shared.Enums.InvitationStatus.Pending)
                                                    {
                                                        <button class="btn btn-outline-primary" onclick="resendInvitation(@invitation.Id)">
                                                            <i class="fas fa-redo"></i>
                                                        </button>
                                                        <button class="btn btn-outline-danger" onclick="cancelInvitation(@invitation.Id)">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted small">-</span>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-envelope-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Invitations Sent</h5>
                            <p class="text-muted mb-4">You haven't sent any invitations for this meeting yet.</p>
                            <a asp-action="SendInvitation" asp-route-id="@meeting?.Id" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>Send Your First Invitation
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function copyInvitationLink() {
            const link = '@(meeting?.InvitationLink ?? "")';
            if (link) {
                copyToClipboard(link, 'Invitation link copied!');
            } else {
                showToast('No invitation link available', 'warning');
            }
        }

        function copyMeetingCode() {
            const code = '@(meeting?.MeetingCode ?? "")';
            if (code) {
                copyToClipboard(code, 'Meeting code copied!');
            } else {
                showToast('No meeting code available', 'warning');
            }
        }

        function resendInvitation(invitationId) {
            if (confirm('Are you sure you want to resend this invitation?')) {
                // TODO: Implement resend invitation API call
                showToast('Invitation resent successfully!', 'success');
            }
        }

        function cancelInvitation(invitationId) {
            if (confirm('Are you sure you want to cancel this invitation? This action cannot be undone.')) {
                // TODO: Implement cancel invitation API call
                showToast('Invitation cancelled successfully!', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }
        }
    </script>
}
