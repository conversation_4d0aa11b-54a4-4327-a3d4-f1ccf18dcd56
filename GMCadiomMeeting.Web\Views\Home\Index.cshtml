﻿@{
    ViewData["Title"] = "Welcome to GMCadiom Meeting";
}

<div class="hero-section bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">Connect, Collaborate, Communicate</h1>
                <p class="lead mb-4">Professional video conferencing made simple. Join meetings instantly or schedule for later with our powerful platform.</p>
                <div class="d-flex gap-3 flex-wrap">
                    <a asp-controller="Meeting" asp-action="Join" class="btn btn-light btn-lg">
                        <i class="fas fa-video me-2"></i>Join Meeting
                    </a>
                    <a asp-controller="Account" asp-action="Register" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-user-plus me-2"></i>Get Started
                    </a>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <div class="video-preview-placeholder bg-white bg-opacity-10 rounded-3 p-5">
                    <i class="fas fa-play-circle fa-5x mb-3"></i>
                    <p class="mb-0">Video Conferencing Preview</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container my-5">
    <div class="row">
        <div class="col-lg-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="feature-icon bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-users fa-lg"></i>
                    </div>
                    <h5 class="card-title">Group Meetings</h5>
                    <p class="card-text">Host meetings with up to 1000 participants. Perfect for team meetings, webinars, and large conferences.</p>
                </div>
            </div>
        </div>
        <div class="col-lg-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="feature-icon bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-desktop fa-lg"></i>
                    </div>
                    <h5 class="card-title">Screen Sharing</h5>
                    <p class="card-text">Share your screen, applications, or browser tabs with participants for effective presentations and collaboration.</p>
                </div>
            </div>
        </div>
        <div class="col-lg-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="feature-icon bg-info text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-comments fa-lg"></i>
                    </div>
                    <h5 class="card-title">Real-time Chat</h5>
                    <p class="card-text">Communicate with participants through public and private messaging during meetings.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="bg-light py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h2 class="mb-4">Ready to get started?</h2>
                <p class="lead mb-4">Join thousands of users who trust GMCadiom Meeting for their video conferencing needs.</p>
                <div class="d-flex gap-3 justify-content-center flex-wrap">
                    <a asp-controller="Account" asp-action="Register" class="btn btn-primary btn-lg">
                        <i class="fas fa-rocket me-2"></i>Start Free Trial
                    </a>
                    <a asp-controller="Meeting" asp-action="Join" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-play me-2"></i>Join a Meeting
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
