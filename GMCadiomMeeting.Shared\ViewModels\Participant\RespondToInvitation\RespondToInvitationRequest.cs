using GMCadiomMeeting.Shared.Enums;
using System.ComponentModel.DataAnnotations;

namespace GMCadiomMeeting.Shared.ViewModels.Participant.RespondToInvitation;

/// <summary>
/// Request model for responding to an invitation
/// </summary>
public class RespondToInvitationRequest
{
    /// <summary>
    /// Invitation ID or token
    /// </summary>
    [Required(ErrorMessage = "Invitation ID or token is required")]
    public string InvitationIdentifier { get; set; } = string.Empty;

    /// <summary>
    /// Response to the invitation
    /// </summary>
    [Required(ErrorMessage = "Response is required")]
    public InvitationStatus Response { get; set; }

    /// <summary>
    /// Optional message with the response
    /// </summary>
    [StringLength(500, ErrorMessage = "Response message cannot exceed 500 characters")]
    public string? ResponseMessage { get; set; }
}
