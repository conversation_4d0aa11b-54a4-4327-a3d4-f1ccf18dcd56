using System.Globalization;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;

namespace GMCadiomMeeting.Shared.Extensions;

/// <summary>
/// Extension methods for string operations
/// </summary>
public static class StringExtensions
{
    /// <summary>
    /// Checks if string is null or whitespace
    /// </summary>
    /// <param name="value">String to check</param>
    /// <returns>True if null or whitespace</returns>
    public static bool IsNullOrWhiteSpace(this string? value)
    {
        return string.IsNullOrWhiteSpace(value);
    }

    /// <summary>
    /// Checks if string has value (not null or whitespace)
    /// </summary>
    /// <param name="value">String to check</param>
    /// <returns>True if has value</returns>
    public static bool HasValue(this string? value)
    {
        return !string.IsNullOrWhiteSpace(value);
    }

    /// <summary>
    /// Truncates string to specified length with ellipsis
    /// </summary>
    /// <param name="value">String to truncate</param>
    /// <param name="maxLength">Maximum length</param>
    /// <param name="ellipsis">Ellipsis string</param>
    /// <returns>Truncated string</returns>
    public static string Truncate(this string value, int maxLength, string ellipsis = "...")
    {
        if (string.IsNullOrEmpty(value) || value.Length <= maxLength)
            return value;

        return value.Substring(0, maxLength - ellipsis.Length) + ellipsis;
    }

    /// <summary>
    /// Converts string to title case
    /// </summary>
    /// <param name="value">String to convert</param>
    /// <returns>Title case string</returns>
    public static string ToTitleCase(this string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return value;

        return CultureInfo.CurrentCulture.TextInfo.ToTitleCase(value.ToLower());
    }

    /// <summary>
    /// Converts string to camel case
    /// </summary>
    /// <param name="value">String to convert</param>
    /// <returns>Camel case string</returns>
    public static string ToCamelCase(this string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return value;

        var words = value.Split(new[] { ' ', '_', '-' }, StringSplitOptions.RemoveEmptyEntries);
        if (words.Length == 0)
            return value;

        var result = words[0].ToLower();
        for (int i = 1; i < words.Length; i++)
        {
            result += char.ToUpper(words[i][0]) + words[i].Substring(1).ToLower();
        }

        return result;
    }

    /// <summary>
    /// Converts string to pascal case
    /// </summary>
    /// <param name="value">String to convert</param>
    /// <returns>Pascal case string</returns>
    public static string ToPascalCase(this string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return value;

        var words = value.Split(new[] { ' ', '_', '-' }, StringSplitOptions.RemoveEmptyEntries);
        var result = new StringBuilder();

        foreach (var word in words)
        {
            if (word.Length > 0)
            {
                result.Append(char.ToUpper(word[0]));
                if (word.Length > 1)
                    result.Append(word.Substring(1).ToLower());
            }
        }

        return result.ToString();
    }

    /// <summary>
    /// Converts string to kebab case
    /// </summary>
    /// <param name="value">String to convert</param>
    /// <returns>Kebab case string</returns>
    public static string ToKebabCase(this string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return value;

        return Regex.Replace(value, @"([a-z])([A-Z])", "$1-$2")
                   .Replace(" ", "-")
                   .Replace("_", "-")
                   .ToLower();
    }

    /// <summary>
    /// Removes HTML tags from string
    /// </summary>
    /// <param name="value">String with HTML</param>
    /// <returns>Plain text string</returns>
    public static string StripHtml(this string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return value;

        return Regex.Replace(value, "<.*?>", string.Empty);
    }

    /// <summary>
    /// Validates email format
    /// </summary>
    /// <param name="email">Email to validate</param>
    /// <returns>True if valid email</returns>
    public static bool IsValidEmail(this string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false;

        try
        {
            var regex = new Regex(@"^[^\s@]+@[^\s@]+\.[^\s@]+$", RegexOptions.IgnoreCase);
            return regex.IsMatch(email);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Validates URL format
    /// </summary>
    /// <param name="url">URL to validate</param>
    /// <returns>True if valid URL</returns>
    public static bool IsValidUrl(this string url)
    {
        if (string.IsNullOrWhiteSpace(url))
            return false;

        return Uri.TryCreate(url, UriKind.Absolute, out var result) &&
               (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps);
    }

    /// <summary>
    /// Generates a random string
    /// </summary>
    /// <param name="length">Length of string</param>
    /// <param name="includeNumbers">Include numbers</param>
    /// <param name="includeSpecialChars">Include special characters</param>
    /// <returns>Random string</returns>
    public static string GenerateRandomString(int length, bool includeNumbers = true, bool includeSpecialChars = false)
    {
        const string letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        const string numbers = "0123456789";
        const string specialChars = "!@#$%^&*()_+-=[]{}|;:,.<>?";

        var chars = letters;
        if (includeNumbers) chars += numbers;
        if (includeSpecialChars) chars += specialChars;

        var random = new Random();
        return new string(Enumerable.Repeat(chars, length)
            .Select(s => s[random.Next(s.Length)]).ToArray());
    }

    /// <summary>
    /// Generates a secure random token
    /// </summary>
    /// <param name="length">Token length</param>
    /// <returns>Secure random token</returns>
    public static string GenerateSecureToken(int length = 32)
    {
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[length];
        rng.GetBytes(bytes);
        return Convert.ToBase64String(bytes).Replace("+", "").Replace("/", "").Replace("=", "").Substring(0, length);
    }

    /// <summary>
    /// Masks email address for privacy
    /// </summary>
    /// <param name="email">Email to mask</param>
    /// <returns>Masked email</returns>
    public static string MaskEmail(this string email)
    {
        if (string.IsNullOrWhiteSpace(email) || !email.Contains('@'))
            return email;

        var parts = email.Split('@');
        var username = parts[0];
        var domain = parts[1];

        if (username.Length <= 2)
            return $"{username[0]}***@{domain}";

        var maskedUsername = username[0] + new string('*', username.Length - 2) + username[^1];
        return $"{maskedUsername}@{domain}";
    }

    /// <summary>
    /// Formats meeting code with dashes
    /// </summary>
    /// <param name="code">Meeting code</param>
    /// <returns>Formatted meeting code</returns>
    public static string FormatMeetingCode(this string code)
    {
        if (string.IsNullOrWhiteSpace(code))
            return code;

        // Remove any existing formatting
        var cleanCode = Regex.Replace(code, @"[^\d]", "");

        // Format as XXX-XXX-XXX
        if (cleanCode.Length == 9)
        {
            return $"{cleanCode.Substring(0, 3)}-{cleanCode.Substring(3, 3)}-{cleanCode.Substring(6, 3)}";
        }

        return code;
    }

    /// <summary>
    /// Extracts initials from a name
    /// </summary>
    /// <param name="name">Full name</param>
    /// <param name="maxInitials">Maximum number of initials</param>
    /// <returns>Initials</returns>
    public static string GetInitials(this string name, int maxInitials = 2)
    {
        if (string.IsNullOrWhiteSpace(name))
            return "";

        var words = name.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
        var initials = new StringBuilder();

        for (int i = 0; i < Math.Min(words.Length, maxInitials); i++)
        {
            if (words[i].Length > 0)
                initials.Append(char.ToUpper(words[i][0]));
        }

        return initials.ToString();
    }

    /// <summary>
    /// Converts string to slug format
    /// </summary>
    /// <param name="value">String to convert</param>
    /// <returns>Slug string</returns>
    public static string ToSlug(this string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return value;

        // Convert to lowercase
        value = value.ToLower();

        // Remove invalid characters
        value = Regex.Replace(value, @"[^a-z0-9\s-]", "");

        // Replace spaces and multiple dashes with single dash
        value = Regex.Replace(value, @"[\s-]+", "-");

        // Trim dashes from start and end
        return value.Trim('-');
    }

    /// <summary>
    /// Counts words in a string
    /// </summary>
    /// <param name="value">String to count</param>
    /// <returns>Word count</returns>
    public static int WordCount(this string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return 0;

        return value.Split(new[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries).Length;
    }

    /// <summary>
    /// Reverses a string
    /// </summary>
    /// <param name="value">String to reverse</param>
    /// <returns>Reversed string</returns>
    public static string Reverse(this string value)
    {
        if (string.IsNullOrEmpty(value))
            return value;

        var chars = value.ToCharArray();
        Array.Reverse(chars);
        return new string(chars);
    }

    /// <summary>
    /// Checks if string contains any of the specified values
    /// </summary>
    /// <param name="value">String to check</param>
    /// <param name="values">Values to search for</param>
    /// <param name="comparison">String comparison type</param>
    /// <returns>True if contains any value</returns>
    public static bool ContainsAny(this string value, IEnumerable<string> values, StringComparison comparison = StringComparison.OrdinalIgnoreCase)
    {
        if (string.IsNullOrEmpty(value) || values == null)
            return false;

        return values.Any(v => value.Contains(v, comparison));
    }
}
