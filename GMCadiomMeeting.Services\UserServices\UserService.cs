﻿using GMCadiomMeeting.Data.Context;
using GMCadiomMeeting.Data.Models;
using GMCadiomMeeting.Shared.ViewModels.User;
using GMCadiomMeeting.Shared.ViewModels.User.Login;
using GMCadiomMeeting.Shared.ViewModels.User.RegisterUser;
using GMCadiomMeeting.Shared.ViewModels.User.UpdateUser;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using System.Text;

namespace GMCadiomMeeting.Services.UserServices
{
    public class UserService(GMCadiomMeetingDbContext context, ILogger<UserService> logger)
    {
        /// <summary>
        /// Register a new user
        /// </summary>
        public async Task<UserDto> Register(RegisterUserRequest request)
        {
            try
            {
                // Check if user already exists
                var existingUser = await context.Users
                    .FirstOrDefaultAsync(u => u.Email.ToLower() == request.Email.ToLower());

                if (existingUser != null)
                {
                    throw new Exception("A user with this email already exists");
                }

                // Generate password hash and salt
                var (passwordHash, passwordSalt) = HashPassword(request.Password);

                var user = new User
                {
                    Email = request.Email.ToLower(),
                    DisplayName = request.DisplayName,
                    FirstName = request.FirstName,
                    LastName = request.LastName,
                    PasswordHash = passwordHash,
                    PasswordSalt = passwordSalt,
                    TimeZone = request.TimeZone,
                    IsActive = true,
                    IsEmailVerified = false
                };

                context.Users.Add(user);
                await context.SaveChangesAsync();

                var response = new UserDto
                {
                    Id = user.Id,
                    Email = user.Email,
                    DisplayName = user.DisplayName,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    ProfilePictureUrl = user.ProfilePictureUrl,
                    TimeZone = user.TimeZone,
                    IsActive = user.IsActive,
                    IsEmailVerified = user.IsEmailVerified,
                    CreatedAt = user.CreatedAt
                };

                return response;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error registering user with email {Email}", request.Email);
                throw new Exception("An error occurred while registering the user");
            }
        }

        /// <summary>
        /// Authenticate user login
        /// </summary>
        public async Task<LoginResponse> Login(LoginRequest request)
        {
            try
            {
                var user = await context.Users
                    .FirstOrDefaultAsync(u => u.Email.ToLower() == request.Email.ToLower() && u.IsActive);

                if (user == null)
                {
                    throw new UnauthorizedAccessException("Invalid email or password");
                }

                if (!VerifyPassword(request.Password, user.PasswordHash, user.PasswordSalt))
                {
                    throw new UnauthorizedAccessException("Invalid email or password");
                }

                // Update last login time
                user.LastLoginAt = DateTime.UtcNow;
                user.UpdatedAt = DateTime.UtcNow;
                await context.SaveChangesAsync();

                var response = new LoginResponse
                {
                    User = new UserDto
                    {
                        Id = user.Id,
                        Email = user.Email,
                        DisplayName = user.DisplayName,
                        FirstName = user.FirstName,
                        LastName = user.LastName,
                        ProfilePictureUrl = user.ProfilePictureUrl,
                        TimeZone = user.TimeZone,
                        IsActive = user.IsActive,
                        IsEmailVerified = user.IsEmailVerified,
                        CreatedAt = user.CreatedAt
                    },
                    // TODO: Generate JWT token
                    Token = "jwt_token_placeholder",
                    ExpiresAt = DateTime.UtcNow.AddHours(24)
                };

                return response;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error during login for email {Email}", request.Email);
                throw new Exception("An error occurred during login");
            }
        }

        /// <summary>
        /// Get user by ID
        /// </summary>
        public async Task<UserDto> GetUser(int id)
        {
            try
            {
                var user = await context.Users
                    .FirstOrDefaultAsync(u => u.Id == id && u.IsActive);

                if (user == null)
                {
                    throw new KeyNotFoundException($"User with ID {id} not found");
                }

                var response = new UserDto
                {
                    Id = user.Id,
                    Email = user.Email,
                    DisplayName = user.DisplayName,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    ProfilePictureUrl = user.ProfilePictureUrl,
                    TimeZone = user.TimeZone,
                    IsActive = user.IsActive,
                    IsEmailVerified = user.IsEmailVerified,
                    CreatedAt = user.CreatedAt
                };

                return response;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving user {UserId}", id);
                throw new Exception("An error occurred while retrieving the user");
            }
        }

        /// <summary>
        /// Update user profile
        /// </summary>
        public async Task<UserDto> UpdateUser(int id, UpdateUserRequest request)
        {
            try
            {
                var user = await context.Users.FindAsync(id);
                if (user == null)
                {
                    throw new KeyNotFoundException($"User with ID {id} not found");
                }

                // Update fields
                if (!string.IsNullOrEmpty(request.DisplayName))
                    user.DisplayName = request.DisplayName;

                if (!string.IsNullOrEmpty(request.FirstName))
                    user.FirstName = request.FirstName;

                if (!string.IsNullOrEmpty(request.LastName))
                    user.LastName = request.LastName;

                if (!string.IsNullOrEmpty(request.ProfilePictureUrl))
                    user.ProfilePictureUrl = request.ProfilePictureUrl;

                if (!string.IsNullOrEmpty(request.TimeZone))
                    user.TimeZone = request.TimeZone;

                user.UpdatedAt = DateTime.UtcNow;

                await context.SaveChangesAsync();

                var response = new UserDto
                {
                    Id = user.Id,
                    Email = user.Email,
                    DisplayName = user.DisplayName,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    ProfilePictureUrl = user.ProfilePictureUrl,
                    TimeZone = user.TimeZone,
                    IsActive = user.IsActive,
                    IsEmailVerified = user.IsEmailVerified,
                    CreatedAt = user.CreatedAt
                };

                return response;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error updating user {UserId}", id);
                throw new Exception("An error occurred while updating the user");
            }
        }

        /// <summary>
        /// Search users by email or name
        /// </summary>
        public async Task<IEnumerable<UserDto>> SearchUsers(string query, int limit = 10)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(query) || query.Length < 2)
                {
                    throw new Exception("Search query must be at least 2 characters long");
                }

                var users = await context.Users
                    .Where(u => u.IsActive &&
                               (u.Email.Contains(query) ||
                                u.DisplayName.Contains(query) ||
                                (u.FirstName != null && u.FirstName.Contains(query)) ||
                                (u.LastName != null && u.LastName.Contains(query))))
                    .Take(limit)
                    .Select(u => new UserDto
                    {
                        Id = u.Id,
                        Email = u.Email,
                        DisplayName = u.DisplayName,
                        FirstName = u.FirstName,
                        LastName = u.LastName,
                        ProfilePictureUrl = u.ProfilePictureUrl,
                        TimeZone = u.TimeZone,
                        IsActive = u.IsActive,
                        IsEmailVerified = u.IsEmailVerified,
                        CreatedAt = u.CreatedAt
                    })
                    .ToListAsync();

                return users;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error searching users with query {Query}", query);
                throw new Exception("An error occurred while searching users");
            }
        }

        private (string hash, string salt) HashPassword(string password)
        {
            using var hmac = new HMACSHA512();
            var salt = Convert.ToBase64String(hmac.Key);
            var hash = Convert.ToBase64String(hmac.ComputeHash(Encoding.UTF8.GetBytes(password)));
            return (hash, salt);
        }

        private bool VerifyPassword(string password, string hash, string salt)
        {
            using var hmac = new HMACSHA512(Convert.FromBase64String(salt));
            var computedHash = Convert.ToBase64String(hmac.ComputeHash(Encoding.UTF8.GetBytes(password)));
            return computedHash == hash;
        }
    }
}