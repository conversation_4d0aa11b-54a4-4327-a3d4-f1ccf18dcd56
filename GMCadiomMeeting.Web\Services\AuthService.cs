using GMCadiomMeeting.Shared.ViewModels.User;
using GMCadiomMeeting.Shared.ViewModels.User.Login;
using GMCadiomMeeting.Shared.ViewModels.User.RegisterUser;
using Newtonsoft.Json;

namespace GMCadiomMeeting.Web.Services;

public interface IAuthService
{
    Task<bool> LoginAsync(LoginRequest request);
    Task LogoutAsync();
    Task<bool> RegisterAsync(RegisterUserRequest request);
    Task<UserDto?> GetCurrentUserAsync();
    bool IsAuthenticated();
    int? GetCurrentUserId();
    string? GetCurrentUserName();
    Task<bool> RefreshUserAsync();
}

public class AuthService : IAuthService
{
    private readonly IApiService _apiService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILogger<AuthService> _logger;
    private const string UserSessionKey = "CurrentUser";
    private const string TokenSessionKey = "AuthToken";

    public AuthService(IApiService apiService, IHttpContextAccessor httpContextAccessor, ILogger<AuthService> logger)
    {
        _apiService = apiService;
        _httpContextAccessor = httpContextAccessor;
        _logger = logger;
    }

    public async Task<bool> LoginAsync(LoginRequest request)
    {
        try
        {
            var loginResponse = await _apiService.LoginAsync(request);

            if (loginResponse != null)
            {
                // Store user and token in session
                var session = _httpContextAccessor.HttpContext?.Session;
                if (session != null)
                {
                    session.SetString(UserSessionKey, JsonConvert.SerializeObject(loginResponse.User));
                    session.SetString(TokenSessionKey, loginResponse.Token);

                    _logger.LogInformation("User {UserId} logged in successfully", loginResponse.User.Id);
                    return true;
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login for user {Email}", request.Email);
            return false;
        }
    }

    public async Task LogoutAsync()
    {
        try
        {
            var session = _httpContextAccessor.HttpContext?.Session;
            if (session != null)
            {
                var userId = GetCurrentUserId();
                session.Remove(UserSessionKey);
                session.Remove(TokenSessionKey);
                session.Clear();

                _logger.LogInformation("User {UserId} logged out", userId);
            }

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout");
        }
    }

    public async Task<bool> RegisterAsync(RegisterUserRequest request)
    {
        try
        {
            var user = await _apiService.RegisterAsync(request);

            if (user != null)
            {
                // Auto-login after successful registration
                var loginModel = new LoginRequest
                {
                    Email = request.Email,
                    Password = request.Password
                };

                return await LoginAsync(loginModel);
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during registration for user {Email}", request.Email);
            return false;
        }
    }

    public async Task<UserDto?> GetCurrentUserAsync()
    {
        try
        {
            var session = _httpContextAccessor.HttpContext?.Session;
            if (session != null)
            {
                var userJson = session.GetString(UserSessionKey);
                if (!string.IsNullOrEmpty(userJson))
                {
                    var user = JsonConvert.DeserializeObject<UserDto>(userJson);

                    // Optionally refresh user data from API
                    if (user != null)
                    {
                        var refreshedUser = await _apiService.GetUserAsync(user.Id);
                        if (refreshedUser != null)
                        {
                            // Update session with fresh data
                            session.SetString(UserSessionKey, JsonConvert.SerializeObject(refreshedUser));
                            return refreshedUser;
                        }
                    }

                    return user;
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current user");
            return null;
        }
    }

    public bool IsAuthenticated()
    {
        try
        {
            var session = _httpContextAccessor.HttpContext?.Session;
            if (session != null)
            {
                var userJson = session.GetString(UserSessionKey);
                var token = session.GetString(TokenSessionKey);

                return !string.IsNullOrEmpty(userJson) && !string.IsNullOrEmpty(token);
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking authentication status");
            return false;
        }
    }

    public int? GetCurrentUserId()
    {
        try
        {
            var session = _httpContextAccessor.HttpContext?.Session;
            if (session != null)
            {
                var userJson = session.GetString(UserSessionKey);
                if (!string.IsNullOrEmpty(userJson))
                {
                    var user = JsonConvert.DeserializeObject<UserDto>(userJson);
                    return user?.Id;
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current user ID");
            return null;
        }
    }

    public string? GetCurrentUserName()
    {
        try
        {
            var session = _httpContextAccessor.HttpContext?.Session;
            if (session != null)
            {
                var userJson = session.GetString(UserSessionKey);
                if (!string.IsNullOrEmpty(userJson))
                {
                    var user = JsonConvert.DeserializeObject<UserDto>(userJson);
                    return user?.DisplayName;
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current user name");
            return null;
        }
    }

    public async Task<bool> RefreshUserAsync()
    {
        try
        {
            var userId = GetCurrentUserId();
            if (userId.HasValue)
            {
                var refreshedUser = await _apiService.GetUserAsync(userId.Value);
                if (refreshedUser != null)
                {
                    var session = _httpContextAccessor.HttpContext?.Session;
                    if (session != null)
                    {
                        session.SetString(UserSessionKey, JsonConvert.SerializeObject(refreshedUser));
                        return true;
                    }
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing user data");
            return false;
        }
    }

    public string? GetAuthToken()
    {
        try
        {
            var session = _httpContextAccessor.HttpContext?.Session;
            return session?.GetString(TokenSessionKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting auth token");
            return null;
        }
    }
}
