using System.ComponentModel.DataAnnotations;

namespace GMCadiomMeeting.Shared.ViewModels.Participant.MuteParticipant;

/// <summary>
/// Request model for muting/unmuting a participant
/// </summary>
public class MuteParticipantRequest
{
    /// <summary>
    /// Meeting ID
    /// </summary>
    [Required(ErrorMessage = "Meeting ID is required")]
    public int MeetingId { get; set; }

    /// <summary>
    /// Participant ID to mute/unmute
    /// </summary>
    [Required(ErrorMessage = "Participant ID is required")]
    public int ParticipantId { get; set; }

    /// <summary>
    /// True to mute, false to unmute
    /// </summary>
    public bool IsMuted { get; set; }
}
