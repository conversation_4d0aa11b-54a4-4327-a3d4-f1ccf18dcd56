using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace GMCadiomMeeting.Data.Context;

/// <summary>
/// Design-time factory for creating DbContext instances during migrations
/// </summary>
public class GMCadiomMeetingDbContextFactory : IDesignTimeDbContextFactory<GMCadiomMeetingDbContext>
{
    public GMCadiomMeetingDbContext CreateDbContext(string[] args)
    {
        var optionsBuilder = new DbContextOptionsBuilder<GMCadiomMeetingDbContext>();

        // Use a dummy connection string for design-time operations (migrations)
        var connectionString = "Server=localhost;Database=GMCadiomMeeting;User=root;Password=***;";

        optionsBuilder.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));

        return new GMCadiomMeetingDbContext(optionsBuilder.Options);
    }
}
