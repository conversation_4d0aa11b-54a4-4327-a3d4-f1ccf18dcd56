using GMCadiomMeeting.Shared.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GMCadiomMeeting.Data.Models;

/// <summary>
/// Represents an invitation to join a meeting
/// </summary>
[Table("Invitations")]
public class Invitation
{
    /// <summary>
    /// Unique identifier for the invitation
    /// </summary>
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    /// <summary>
    /// Unique token for the invitation (used in invitation links)
    /// </summary>
    [Required]
    [StringLength(255)]
    [Column(TypeName = "varchar(255)")]
    public string InvitationToken { get; set; } = string.Empty;

    /// <summary>
    /// Email address of the invitee (for external users)
    /// </summary>
    [EmailAddress]
    [StringLength(255)]
    [Column(TypeName = "varchar(255)")]
    public string? InviteeEmail { get; set; }

    /// <summary>
    /// Name of the invitee (for external users)
    /// </summary>
    [StringLength(100)]
    [Column(TypeName = "varchar(100)")]
    public string? InviteeName { get; set; }

    /// <summary>
    /// Current status of the invitation
    /// </summary>
    [Required]
    public InvitationStatus Status { get; set; } = InvitationStatus.Pending;

    /// <summary>
    /// Method used to send the invitation
    /// </summary>
    [Required]
    public InvitationMethod Method { get; set; } = InvitationMethod.Email;

    /// <summary>
    /// Role the invitee will have in the meeting
    /// </summary>
    [Required]
    public ParticipantRole InvitedRole { get; set; } = ParticipantRole.Attendee;

    /// <summary>
    /// Personal message included with the invitation
    /// </summary>
    [StringLength(500)]
    [Column(TypeName = "text")]
    public string? PersonalMessage { get; set; }

    /// <summary>
    /// When the invitation was sent
    /// </summary>
    [Required]
    public DateTime SentAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// When the invitation expires
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// When the invitation was responded to (accepted/declined)
    /// </summary>
    public DateTime? RespondedAt { get; set; }

    /// <summary>
    /// Number of times the invitation has been resent
    /// </summary>
    [Required]
    public int ResendCount { get; set; } = 0;

    /// <summary>
    /// When the invitation was last resent
    /// </summary>
    public DateTime? LastResentAt { get; set; }

    /// <summary>
    /// Whether the invitation allows the invitee to join before the host
    /// </summary>
    [Required]
    public bool AllowJoinBeforeHost { get; set; } = false;

    /// <summary>
    /// Additional invitation settings as JSON
    /// </summary>
    [Column(TypeName = "json")]
    public string? Settings { get; set; }

    /// <summary>
    /// When this invitation record was created
    /// </summary>
    [Required]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// When this invitation record was last updated
    /// </summary>
    [Required]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Foreign Keys

    /// <summary>
    /// ID of the meeting this invitation is for
    /// </summary>
    [Required]
    public int MeetingId { get; set; }

    /// <summary>
    /// ID of the user who sent the invitation
    /// </summary>
    [Required]
    public int SentByUserId { get; set; }

    /// <summary>
    /// ID of the user who was invited (null for external users)
    /// </summary>
    public int? InvitedUserId { get; set; }

    // Navigation Properties

    /// <summary>
    /// The meeting this invitation is for
    /// </summary>
    [ForeignKey(nameof(MeetingId))]
    public virtual Meeting Meeting { get; set; } = null!;

    /// <summary>
    /// The user who sent the invitation
    /// </summary>
    [ForeignKey(nameof(SentByUserId))]
    public virtual User SentByUser { get; set; } = null!;

    /// <summary>
    /// The user who was invited (null for external users)
    /// </summary>
    [ForeignKey(nameof(InvitedUserId))]
    public virtual User? InvitedUser { get; set; }
}
