using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace GMCadiomMeeting.Shared.Validation;

/// <summary>
/// Validates that the end date is after the start date
/// </summary>
public class EndDateAfterStartDateAttribute : ValidationAttribute
{
    private readonly string _startDatePropertyName;

    public EndDateAfterStartDateAttribute(string startDatePropertyName)
    {
        _startDatePropertyName = startDatePropertyName;
    }

    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        var endDate = value as DateTime?;
        if (!endDate.HasValue)
        {
            return ValidationResult.Success;
        }

        var startDateProperty = validationContext.ObjectType.GetProperty(_startDatePropertyName);
        if (startDateProperty == null)
        {
            return new ValidationResult($"Property {_startDatePropertyName} not found");
        }

        var startDate = startDateProperty.GetValue(validationContext.ObjectInstance) as DateTime?;
        if (!startDate.HasValue)
        {
            return ValidationResult.Success;
        }

        if (endDate <= startDate)
        {
            return new ValidationResult(ErrorMessage ?? "End date must be after start date");
        }

        return ValidationResult.Success;
    }
}

/// <summary>
/// Validates that the date is in the future
/// </summary>
public class FutureDateAttribute : ValidationAttribute
{
    private readonly int _minimumMinutesFromNow;

    public FutureDateAttribute(int minimumMinutesFromNow = 0)
    {
        _minimumMinutesFromNow = minimumMinutesFromNow;
    }

    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value is not DateTime dateTime)
        {
            return ValidationResult.Success;
        }

        var minimumDateTime = DateTime.UtcNow.AddMinutes(_minimumMinutesFromNow);
        if (dateTime <= minimumDateTime)
        {
            var message = _minimumMinutesFromNow > 0
                ? $"Date must be at least {_minimumMinutesFromNow} minutes in the future"
                : "Date must be in the future";
            return new ValidationResult(ErrorMessage ?? message);
        }

        return ValidationResult.Success;
    }
}

/// <summary>
/// Validates a list of email addresses
/// </summary>
public class EmailListAttribute : ValidationAttribute
{
    private readonly int _maxEmails;
    private readonly bool _allowDuplicates;

    public EmailListAttribute(int maxEmails = 50, bool allowDuplicates = false)
    {
        _maxEmails = maxEmails;
        _allowDuplicates = allowDuplicates;
    }

    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value is not List<string> emails)
        {
            return ValidationResult.Success;
        }

        if (emails.Count == 0)
        {
            return new ValidationResult("At least one email address is required");
        }

        if (emails.Count > _maxEmails)
        {
            return new ValidationResult($"Maximum {_maxEmails} email addresses allowed");
        }

        var emailRegex = new Regex(@"^[^\s@]+@[^\s@]+\.[^\s@]+$", RegexOptions.IgnoreCase);
        var invalidEmails = emails.Where(email => !emailRegex.IsMatch(email)).ToList();

        if (invalidEmails.Any())
        {
            return new ValidationResult($"Invalid email addresses: {string.Join(", ", invalidEmails)}");
        }

        if (!_allowDuplicates)
        {
            var duplicates = emails.GroupBy(e => e.ToLowerInvariant())
                                  .Where(g => g.Count() > 1)
                                  .Select(g => g.Key)
                                  .ToList();

            if (duplicates.Any())
            {
                return new ValidationResult($"Duplicate email addresses found: {string.Join(", ", duplicates)}");
            }
        }

        return ValidationResult.Success;
    }
}

/// <summary>
/// Validates meeting code format
/// </summary>
public class MeetingCodeAttribute : ValidationAttribute
{
    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value is not string meetingCode)
        {
            return ValidationResult.Success;
        }

        // Meeting code should be in format XXX-XXX-XXX (9 digits with dashes)
        var regex = new Regex(@"^\d{3}-\d{3}-\d{3}$");
        if (!regex.IsMatch(meetingCode))
        {
            return new ValidationResult(ErrorMessage ?? "Meeting code must be in format XXX-XXX-XXX");
        }

        return ValidationResult.Success;
    }
}

/// <summary>
/// Validates password strength
/// </summary>
public class StrongPasswordAttribute : ValidationAttribute
{
    private readonly bool _requireUppercase;
    private readonly bool _requireLowercase;
    private readonly bool _requireDigit;
    private readonly bool _requireSpecialChar;
    private readonly int _minLength;

    public StrongPasswordAttribute(
        bool requireUppercase = true,
        bool requireLowercase = true,
        bool requireDigit = true,
        bool requireSpecialChar = false,
        int minLength = 8)
    {
        _requireUppercase = requireUppercase;
        _requireLowercase = requireLowercase;
        _requireDigit = requireDigit;
        _requireSpecialChar = requireSpecialChar;
        _minLength = minLength;
    }

    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value is not string password)
        {
            return ValidationResult.Success;
        }

        var errors = new List<string>();

        if (password.Length < _minLength)
        {
            errors.Add($"Password must be at least {_minLength} characters long");
        }

        if (_requireUppercase && !password.Any(char.IsUpper))
        {
            errors.Add("Password must contain at least one uppercase letter");
        }

        if (_requireLowercase && !password.Any(char.IsLower))
        {
            errors.Add("Password must contain at least one lowercase letter");
        }

        if (_requireDigit && !password.Any(char.IsDigit))
        {
            errors.Add("Password must contain at least one digit");
        }

        if (_requireSpecialChar && !password.Any(ch => !char.IsLetterOrDigit(ch)))
        {
            errors.Add("Password must contain at least one special character");
        }

        if (errors.Any())
        {
            return new ValidationResult(string.Join(". ", errors));
        }

        return ValidationResult.Success;
    }
}

/// <summary>
/// Validates timezone string
/// </summary>
public class TimeZoneAttribute : ValidationAttribute
{
    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value is not string timeZone)
        {
            return ValidationResult.Success;
        }

        try
        {
            TimeZoneInfo.FindSystemTimeZoneById(timeZone);
            return ValidationResult.Success;
        }
        catch (TimeZoneNotFoundException)
        {
            return new ValidationResult(ErrorMessage ?? "Invalid timezone identifier");
        }
        catch (InvalidTimeZoneException)
        {
            return new ValidationResult(ErrorMessage ?? "Invalid timezone identifier");
        }
    }
}

/// <summary>
/// Validates file size
/// </summary>
public class FileSizeAttribute : ValidationAttribute
{
    private readonly long _maxSizeInBytes;

    public FileSizeAttribute(long maxSizeInMB)
    {
        _maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    }

    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value is not long fileSize)
        {
            return ValidationResult.Success;
        }

        if (fileSize > _maxSizeInBytes)
        {
            var maxSizeMB = _maxSizeInBytes / (1024 * 1024);
            return new ValidationResult(ErrorMessage ?? $"File size cannot exceed {maxSizeMB} MB");
        }

        return ValidationResult.Success;
    }
}

/// <summary>
/// Validates allowed file extensions
/// </summary>
public class AllowedExtensionsAttribute : ValidationAttribute
{
    private readonly string[] _allowedExtensions;

    public AllowedExtensionsAttribute(params string[] allowedExtensions)
    {
        _allowedExtensions = allowedExtensions.Select(ext => ext.ToLowerInvariant()).ToArray();
    }

    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value is not string fileName)
        {
            return ValidationResult.Success;
        }

        var extension = Path.GetExtension(fileName)?.ToLowerInvariant();
        if (string.IsNullOrEmpty(extension))
        {
            return new ValidationResult("File must have an extension");
        }

        if (!_allowedExtensions.Contains(extension))
        {
            return new ValidationResult($"File extension {extension} is not allowed. Allowed extensions: {string.Join(", ", _allowedExtensions)}");
        }

        return ValidationResult.Success;
    }
}

/// <summary>
/// Validates that a collection has a minimum number of items
/// </summary>
public class MinItemsAttribute : ValidationAttribute
{
    private readonly int _minItems;

    public MinItemsAttribute(int minItems)
    {
        _minItems = minItems;
    }

    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value is not System.Collections.ICollection collection)
        {
            return ValidationResult.Success;
        }

        if (collection.Count < _minItems)
        {
            return new ValidationResult(ErrorMessage ?? $"At least {_minItems} items are required");
        }

        return ValidationResult.Success;
    }
}

/// <summary>
/// Validates that a collection has a maximum number of items
/// </summary>
public class MaxItemsAttribute : ValidationAttribute
{
    private readonly int _maxItems;

    public MaxItemsAttribute(int maxItems)
    {
        _maxItems = maxItems;
    }

    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value is not System.Collections.ICollection collection)
        {
            return ValidationResult.Success;
        }

        if (collection.Count > _maxItems)
        {
            return new ValidationResult(ErrorMessage ?? $"Maximum {_maxItems} items are allowed");
        }

        return ValidationResult.Success;
    }
}
