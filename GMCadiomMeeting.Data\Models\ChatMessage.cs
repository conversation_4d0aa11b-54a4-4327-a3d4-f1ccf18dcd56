using GMCadiomMeeting.Shared.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GMCadiomMeeting.Data.Models;

/// <summary>
/// Represents a chat message sent during a meeting
/// </summary>
[Table("ChatMessages")]
public class ChatMessage
{
    /// <summary>
    /// Unique identifier for the chat message
    /// </summary>
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    /// <summary>
    /// Content of the message
    /// </summary>
    [Required]
    [StringLength(2000)]
    [Column(TypeName = "text")]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Type of the message (text, file, image, etc.)
    /// </summary>
    [Required]
    public MessageType Type { get; set; } = MessageType.Text;

    /// <summary>
    /// Scope of the message (public, private, etc.)
    /// </summary>
    [Required]
    public MessageScope Scope { get; set; } = MessageScope.Public;

    /// <summary>
    /// Whether the message has been edited
    /// </summary>
    [Required]
    public bool IsEdited { get; set; } = false;

    /// <summary>
    /// Whether the message has been deleted
    /// </summary>
    [Required]
    public bool IsDeleted { get; set; } = false;

    /// <summary>
    /// Original content before editing (for audit purposes)
    /// </summary>
    [StringLength(2000)]
    [Column(TypeName = "text")]
    public string? OriginalContent { get; set; }

    /// <summary>
    /// URL to attached file (if message type is File or Image)
    /// </summary>
    [StringLength(500)]
    [Column(TypeName = "varchar(500)")]
    public string? FileUrl { get; set; }

    /// <summary>
    /// Name of attached file
    /// </summary>
    [StringLength(255)]
    [Column(TypeName = "varchar(255)")]
    public string? FileName { get; set; }

    /// <summary>
    /// Size of attached file in bytes
    /// </summary>
    public long? FileSize { get; set; }

    /// <summary>
    /// MIME type of attached file
    /// </summary>
    [StringLength(100)]
    [Column(TypeName = "varchar(100)")]
    public string? FileMimeType { get; set; }

    /// <summary>
    /// Thumbnail URL for image/video files
    /// </summary>
    [StringLength(500)]
    [Column(TypeName = "varchar(500)")]
    public string? ThumbnailUrl { get; set; }

    /// <summary>
    /// Message metadata as JSON (reactions, mentions, etc.)
    /// </summary>
    [Column(TypeName = "json")]
    public string? Metadata { get; set; }

    /// <summary>
    /// When the message was sent
    /// </summary>
    [Required]
    public DateTime SentAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// When the message was last edited
    /// </summary>
    public DateTime? EditedAt { get; set; }

    /// <summary>
    /// When the message was deleted
    /// </summary>
    public DateTime? DeletedAt { get; set; }

    // Foreign Keys

    /// <summary>
    /// ID of the meeting this message belongs to
    /// </summary>
    [Required]
    public int MeetingId { get; set; }

    /// <summary>
    /// ID of the user who sent the message
    /// </summary>
    [Required]
    public int SenderId { get; set; }

    /// <summary>
    /// ID of the user who is the recipient (for private messages)
    /// </summary>
    public int? RecipientId { get; set; }

    /// <summary>
    /// ID of the message this is replying to (for threaded conversations)
    /// </summary>
    public int? ReplyToMessageId { get; set; }

    // Navigation Properties

    /// <summary>
    /// The meeting this message belongs to
    /// </summary>
    [ForeignKey(nameof(MeetingId))]
    public virtual Meeting Meeting { get; set; } = null!;

    /// <summary>
    /// The user who sent the message
    /// </summary>
    [ForeignKey(nameof(SenderId))]
    public virtual User Sender { get; set; } = null!;

    /// <summary>
    /// The user who is the recipient (for private messages)
    /// </summary>
    [ForeignKey(nameof(RecipientId))]
    public virtual User? Recipient { get; set; }

    /// <summary>
    /// The message this is replying to (for threaded conversations)
    /// </summary>
    [ForeignKey(nameof(ReplyToMessageId))]
    public virtual ChatMessage? ReplyToMessage { get; set; }

    /// <summary>
    /// Replies to this message
    /// </summary>
    [InverseProperty(nameof(ReplyToMessage))]
    public virtual ICollection<ChatMessage> Replies { get; set; } = new List<ChatMessage>();
}
