using GMCadiomMeeting.Shared.Enums;

namespace GMCadiomMeeting.Shared.ViewModels.Participant;

/// <summary>
/// Meeting invitation information for API responses
/// </summary>
public class InvitationDto
{
    /// <summary>
    /// Invitation unique identifier
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Meeting ID
    /// </summary>
    public int MeetingId { get; set; }

    /// <summary>
    /// Meeting title
    /// </summary>
    public string? MeetingTitle { get; set; }

    /// <summary>
    /// Meeting scheduled start time
    /// </summary>
    public DateTime? MeetingScheduledStart { get; set; }

    /// <summary>
    /// User ID who sent the invitation
    /// </summary>
    public int SentByUserId { get; set; }

    /// <summary>
    /// Name of user who sent the invitation
    /// </summary>
    public string? SentByUser { get; set; }

    /// <summary>
    /// Email address of the invitee
    /// </summary>
    public string InviteeEmail { get; set; } = string.Empty;

    /// <summary>
    /// User ID of the invitee (if registered user)
    /// </summary>
    public int? InviteeUserId { get; set; }

    /// <summary>
    /// Role assigned to the invitee
    /// </summary>
    public ParticipantRole InvitedRole { get; set; }

    /// <summary>
    /// Personal message included with the invitation
    /// </summary>
    public string? PersonalMessage { get; set; }

    /// <summary>
    /// Invitation status
    /// </summary>
    public InvitationStatus Status { get; set; }

    /// <summary>
    /// Method used to send the invitation
    /// </summary>
    public InvitationMethod Method { get; set; }

    /// <summary>
    /// Date and time when invitation was sent
    /// </summary>
    public DateTime SentAt { get; set; }

    /// <summary>
    /// Date and time when invitation was responded to
    /// </summary>
    public DateTime? RespondedAt { get; set; }

    /// <summary>
    /// Date and time when invitation expires
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Unique token for the invitation
    /// </summary>
    public string? InvitationToken { get; set; }
}
