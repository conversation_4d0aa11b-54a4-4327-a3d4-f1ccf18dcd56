using GMCadiomMeeting.Shared.Enums;
using GMCadiomMeeting.Web.Models;
using GMCadiomMeeting.Web.Services;
using Microsoft.AspNetCore.Mvc;
using System.Diagnostics;

namespace GMCadiomMeeting.Web.Controllers;

public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly IAuthService _authService;
    private readonly IApiService _apiService;

    public HomeController(ILogger<HomeController> logger, IAuthService authService, IApiService apiService)
    {
        _logger = logger;
        _authService = authService;
        _apiService = apiService;
    }

    public async Task<IActionResult> Index()
    {
        if (_authService.IsAuthenticated())
        {
            return RedirectToAction("Dashboard");
        }

        return View();
    }

    public async Task<IActionResult> Dashboard()
    {
        if (!_authService.IsAuthenticated())
        {
            return RedirectToAction("Index");
        }

        var user = await _authService.GetCurrentUserAsync();
        if (user == null)
        {
            return RedirectToAction("Index");
        }

        var meetings = await _apiService.GetUserMeetingsAsync(user.Id);
        var invitations = await _apiService.GetUserInvitationsAsync(user.Id);

        ViewBag.User = user;
        ViewBag.UpcomingMeetings = meetings.Where(m => m != null && m.Status == MeetingStatus.Scheduled && m.ScheduledStartTime > DateTime.Now).Take(5);
        ViewBag.PendingInvitations = invitations.Where(i => i.Status == InvitationStatus.Pending).Take(5);

        return View();
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
